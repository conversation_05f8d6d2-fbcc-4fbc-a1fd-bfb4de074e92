name: Bidirectional Sync Validation
on: [push, pull_request]

jobs:
  sync-validation:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0  # Full history for git diff

      - uses: actions/setup-node@v4
        with:
          node-version: '20'

      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: 8.15.4

      - name: Install dependencies
        run: cd code && pnpm install

      - name: Build packages
        run: cd code && pnpm build

      - name: Run incremental sync (dry-run)
        run: cd code && pnpm run sync-kg -- --since origin/main --dry-run ../docs/tech-specs

      - name: Validate coverage thresholds
        run: |
          # Check if coverage meets minimum thresholds
          # Exit 60 if coverage < 0.5, exit 0 otherwise
          cd code && pnpm run sync-kg -- --since origin/main ../docs/tech-specs

      - name: Run tests
        run: cd code && pnpm test

      - name: Check coverage
        run: cd code && pnpm run test:coverage
