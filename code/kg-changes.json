{"timestamp": "2025-06-01T19:26:35.766Z", "summary": {"nodesAdded": 2, "nodesUpdated": 0, "nodesMarkedStale": 0, "edgesAdded": 1, "edgesUpdated": 0, "edgesMarkedStale": 0}, "coverage": [{"milestoneId": "Mm0", "totalComponents": 0, "implementedComponents": 0, "coverage": 1, "confidence": 0, "lastUpdated": "2025-06-01T19:26:35.753Z"}, {"milestoneId": "<PERSON><PERSON><PERSON><PERSON>", "totalComponents": 0, "implementedComponents": 0, "coverage": 1, "confidence": 0, "lastUpdated": "2025-06-01T19:26:35.753Z"}, {"milestoneId": "Mm1", "totalComponents": 0, "implementedComponents": 0, "coverage": 1, "confidence": 0, "lastUpdated": "2025-06-01T19:26:35.753Z"}, {"milestoneId": "Mtest", "totalComponents": 0, "implementedComponents": 0, "coverage": 1, "confidence": 0, "lastUpdated": "2025-06-01T19:26:35.753Z"}, {"milestoneId": "Mimplementation", "totalComponents": 0, "implementedComponents": 0, "coverage": 1, "confidence": 0, "lastUpdated": "2025-06-01T19:26:35.753Z"}, {"milestoneId": "Mtemplate", "totalComponents": 0, "implementedComponents": 0, "coverage": 1, "confidence": 0, "lastUpdated": "2025-06-01T19:26:35.753Z"}], "errors": [{"message": "@implements annotation not attached to a function, class, or method", "severity": "error", "file": "code/packages/kg-cli/src/sync-kg.ts", "line": 1}, {"message": "File name \"sync-kg.ts\" doesn't match component \"CLIIntegration\"", "severity": "warning", "file": "code/packages/kg-cli/src/sync-kg.ts", "line": 1}]}