{"@context": {"@vocab": "https://workflow-mapper.dev/vocab#", "title": "https://schema.org/name", "description": "https://schema.org/description", "implements": "https://workflow-mapper.dev/vocab#implements", "dependsOn": "https://workflow-mapper.dev/vocab#dependsOn"}, "@graph": [{"@id": "spec----docs-tech-specs-adrs-adr-001-monorepo-mdx-adr-001---monorepo-structure-with-pnpm-workspaces", "@type": "ArchitecturalDecision", "filePath": "../docs/tech-specs/adrs/adr-001-monorepo.mdx", "title": "ADR-001 — Monorepo Structure with pnpm Workspaces", "description": "Decision to use pnpm workspaces with apps/ and packages/ structure instead of separate repositories.", "created": "2025-05-25T00:00:00.000Z", "updated": "2025-05-25T00:00:00.000Z", "version": "1.0.0", "status": "Accepted", "tags": ["adr", "architecture", "monorepo"], "authors": ["nitishMeh<PERSON><PERSON>"]}, {"@id": "spec----docs-tech-specs-adrs-adr-002-typescript-mdx-adr-002---typescript-first-development", "@type": "ArchitecturalDecision", "filePath": "../docs/tech-specs/adrs/adr-002-typescript.mdx", "title": "ADR-002 — TypeScript-First Development", "description": "Decision to use strict TypeScript across frontend, backend, and shared code.", "created": "2025-05-25T00:00:00.000Z", "updated": "2025-05-25T00:00:00.000Z", "version": "1.0.0", "status": "Accepted", "tags": ["adr", "architecture", "typescript"], "authors": ["nitishMeh<PERSON><PERSON>"]}, {"@id": "spec----docs-tech-specs-adrs-adr-003-jsonld-mdx-adr-003---json-ld-for-graph-representation", "@type": "ArchitecturalDecision", "filePath": "../docs/tech-specs/adrs/adr-003-jsonld.mdx", "title": "ADR-003 — JSON-LD for Graph Representation", "description": "Decision to use JSON-LD as the canonical format for representing workflow graphs.", "created": "2025-05-25T00:00:00.000Z", "updated": "2025-05-25T00:00:00.000Z", "version": "1.0.0", "status": "Accepted", "tags": ["adr", "architecture", "data-format"], "authors": ["nitishMeh<PERSON><PERSON>"]}, {"@id": "spec----docs-tech-specs-adrs-adr-004-consolidated-gitignore-strateg-mdx-adr-004---consolidated--gitignore-strategy", "@type": "ArchitecturalDecision", "filePath": "../docs/tech-specs/adrs/adr-004-consolidated-gitignore-strateg.mdx", "title": "ADR-004 — Consolidated .gitignore Strategy", "description": "Decision to consolidate multiple .gitignore files into a single root-level file with comprehensive coverage for all project tools and technologies.", "created": "2025-05-29T00:00:00.000Z", "updated": "2025-05-29T00:00:00.000Z", "version": "1.0.0", "status": "Accepted", "tags": ["adr", "repository-management", "tooling"], "authors": ["nitishMeh<PERSON><PERSON>"]}, {"@id": "spec----docs-tech-specs-adrs-adr-005-knowledge-graph-system-archite-mdx-adr-005---knowledge-graph-system-architecture", "@type": "ArchitecturalDecision", "filePath": "../docs/tech-specs/adrs/adr-005-knowledge-graph-system-archite.mdx", "title": "ADR-005 — Knowledge Graph System Architecture", "description": "Unified architecture combining specification parsing (M0.1) and static code analysis (M1.1) for comprehensive knowledge graph generation.", "created": "2025-01-27T00:00:00.000Z", "updated": "2025-01-27T00:00:00.000Z", "version": "1.0.0", "status": "Accepted", "tags": ["adr", "architecture", "knowledge-graph", "static-analysis", "specifications"], "authors": ["WorkflowMapper Team"]}, {"@id": "spec----docs-tech-specs-adrs-log-mdx-architectural-decision-records--adrs-", "@type": "ArchitecturalDecision", "filePath": "../docs/tech-specs/adrs/log.mdx", "title": "Architectural Decision Records (ADRs)", "description": "Log of all significant architectural decisions made during development.", "created": "2025-05-25T00:00:00.000Z", "updated": "2025-05-25T00:00:00.000Z", "version": "0.1.0", "status": "Living", "tags": ["architecture", "decisions"], "authors": ["nitishMeh<PERSON><PERSON>"]}, {"@id": "spec----docs-tech-specs-archived-adrs-adr-007-docusaurus-mdx-adr-007---docusaurus-for-documentation-site", "@type": "ArchitecturalDecision", "filePath": "../docs/tech-specs/archived/adrs/adr-007-docusaurus.mdx", "title": "ADR-007 — Docusaurus for Documentation Site", "description": "Decision to use Docusaurus 2 as the static site generator for rendering technical specifications and project documentation.", "created": "2025-01-25T00:00:00.000Z", "updated": "2025-01-25T00:00:00.000Z", "version": "1.0.0", "status": "Accepted", "tags": ["adr", "architecture", "documentation", "<PERSON>cusaurus"], "authors": ["nitishMeh<PERSON><PERSON>"]}, {"@id": "spec----docs-tech-specs-archived-milestones-milestone-m0-1-content-processing-validation-mdx-content-processing-solution---validation-experiment", "@type": "Milestone", "filePath": "../docs/tech-specs/archived/milestones/milestone-m0.1/content-processing-validation.mdx", "title": "Content Processing Solution - Validation Experiment", "description": "Testing and validating the content processing approach before integration", "created": "2025-01-26T00:00:00.000Z", "version": "0.1.0", "status": "Experimental", "tags": ["experiment", "validation", "content-processing"], "authors": ["nitishMeh<PERSON><PERSON>"]}, {"@id": "spec----docs-tech-specs-archived-milestones-milestone-m0-1-milestone-m0-1-mdx--archived--milestone-m0-1---docusaurus-documentation-site", "@type": "Milestone", "filePath": "../docs/tech-specs/archived/milestones/milestone-m0.1/milestone-M0.1.mdx", "title": "[ARCHIVED] Milestone M0.1 — Docusaurus Documentation Site", "description": "[ARCHIVED] Original attempt at documentation site - superseded by WorkflowMapperAgent approach", "created": "2025-01-25T00:00:00.000Z", "archived": "2025-01-26T00:00:00.000Z", "version": "0.1.0", "status": "Archived", "tags": ["milestone", "documentation", "<PERSON>cusaurus", "archived"], "authors": ["nitishMeh<PERSON><PERSON>"], "archive_reason": "Pivoted to WorkflowMapperAgent foundation after discovering alignment with broader vision", "superseded_by": "milestone-experiment-1.md and upcoming ChatGPT milestone revision"}, {"@id": "spec----docs-tech-specs-archived-milestones-milestone-m0-1-milestone-pivot-decision-mdx-milestone-pivot-decision---from-documentation-to-workflowmapperagent", "@type": "Milestone", "filePath": "../docs/tech-specs/archived/milestones/milestone-m0.1/milestone-pivot-decision.mdx", "title": "Milestone Pivot Decision - From Documentation to WorkflowMapperAgent", "description": "Decision record for pivoting from M0.1 Docusaurus approach to WorkflowMapperAgent foundation", "created": "2025-01-26T00:00:00.000Z", "version": "1.0.0", "status": "Approved", "tags": ["decision", "pivot", "architecture", "workflow-mapper"], "authors": ["nitishMeh<PERSON><PERSON>"]}, {"@id": "spec----docs-tech-specs-dependencies-mdx-dependencies", "@type": "Specification", "filePath": "../docs/tech-specs/dependencies.mdx"}, {"@id": "spec----docs-tech-specs-domains-code-parser-mdx-code-parser-domain", "@type": "Domain", "filePath": "../docs/tech-specs/domains/code-parser.mdx", "title": "Code Parser Domain", "description": "Static code analysis and knowledge graph generation from source files", "version": "1.0.0", "status": "Active", "created": "2025-01-27", "updated": "2025-01-27", "authors": ["WorkflowMapper Team"], "tags": ["code-analysis", "tree-sitter", "knowledge-graph", "static-analysis"]}, {"@id": "spec----docs-tech-specs-guides-agent-configuration-guide-mdx-agent-configuration-guide", "@type": "Specification", "filePath": "../docs/tech-specs/guides/agent-configuration-guide.mdx", "title": "Agent Configuration Guide", "description": "How to configure AI agents with milestone process requirements", "created": "2025-05-25", "updated": "2025-05-25", "version": "1.0.0", "status": "Active", "tags": ["process", "agents", "configuration", "quality"], "authors": ["nitishMeh<PERSON><PERSON>"]}, {"@id": "spec----docs-tech-specs-milestones-log-mdx-milestone-progress-log", "@type": "Milestone", "filePath": "../docs/tech-specs/milestones/log.mdx", "title": "Milestone Progress Log", "description": "Index and progress tracking for all project milestones.", "created": "2025-05-25T00:00:00.000Z", "updated": "2025-05-25T00:00:00.000Z", "version": "0.1.0", "status": "Living", "tags": ["milestones", "progress"], "authors": ["nitishMeh<PERSON><PERSON>"]}, {"@id": "spec----docs-tech-specs-milestones-milestone-m0-1-mdx-milestone-m0-1---knowledge-graph-bootstrap", "@type": "Milestone", "filePath": "../docs/tech-specs/milestones/milestone-M0.1.mdx", "title": "Milestone M0.1 — Knowledge-<PERSON>rap<PERSON> Bootstrap", "description": "Parse existing MDX specs into a JSON-LD + YAML graph; emit CLI tools every agent can run.", "created": "2025-01-25T00:00:00.000Z", "version": "0.2.0", "status": "Draft", "tags": ["milestone"], "authors": ["WorkflowMapper Team"]}, {"@id": "spec----docs-tech-specs-milestones-milestone-m0-mdx-milestone-m0---repository-skeleton---ci", "@type": "Milestone", "filePath": "../docs/tech-specs/milestones/milestone-M0.mdx", "title": "Milestone M0 — Repository Skeleton & CI", "description": "The contractual scope, decisions, and acceptance tests for the very first deliverable.", "created": "2025-05-25T00:00:00.000Z", "updated": "2025-05-25T00:00:00.000Z", "version": "0.5.0", "status": "Completed", "tags": ["milestone"], "authors": ["nitishMeh<PERSON><PERSON>"]}, {"@id": "spec----docs-tech-specs-milestones-milestone-m1-1-mdx-milestone-m1---static-code-parser---graph-augmenter", "@type": "Milestone", "filePath": "../docs/tech-specs/milestones/milestone-M1.1.mdx", "title": "Milestone M1 — Static Code Parser & Graph Augmenter", "description": "Parse Python + JavaScript source files with Tree-sitter, extract functions & call-graph, and merge results into the existing KG (kg.jsonld / kg.yaml).", "created": "2025-05-29T00:00:00.000Z", "version": "0.1.0", "status": "Draft", "tags": ["milestone"], "authors": ["WorkflowMapper Team"]}, {"@id": "spec----docs-tech-specs-milestones-milestone-m1-2-mdx-milestone-m1-1---bidirectional-sync---incremental-diff", "@type": "Milestone", "filePath": "../docs/tech-specs/milestones/milestone-M1.2.mdx", "title": "Milestone M1.1 — Bidirectional Sync & Incremental Diff", "description": "Link code ↔ specs via annotations, update the KG on every git diff, and emit confidence / coverage metrics.", "created": "2025-05-29T00:00:00.000Z", "version": "0.1.0", "status": "Draft", "tags": ["milestone"], "authors": ["WorkflowMapper Team"]}, {"@id": "spec----docs-tech-specs-milestones-milestone-test-mdx-milestone-test---agent-configuration-validation", "@type": "Milestone", "filePath": "../docs/tech-specs/milestones/milestone-TEST.mdx", "title": "Milestone TEST - Agent Configuration Validation", "description": "Test milestone to validate streamlined agent configuration system", "created": "2025-05-25", "updated": "2025-05-25", "version": "1.0.0", "status": "Test", "tags": ["test", "validation", "agent-configuration"], "authors": ["nitishMeh<PERSON><PERSON>"]}, {"@id": "spec----docs-tech-specs-process-agent-rules-augment-mdx-augment-agent-configuration", "@type": "Specification", "filePath": "../docs/tech-specs/process/agent-rules/augment.mdx", "title": "Augment Agent Configuration", "description": "Augment Agent specific configuration for milestone implementation", "created": "2025-05-25", "updated": "2025-05-25", "version": "1.0.0", "status": "Active", "tags": ["agent-rules", "augment", "executable"], "authors": ["nitishMeh<PERSON><PERSON>"]}, {"@id": "spec----docs-tech-specs-process-agent-rules-claude-mdx-claude-anthropic-agent-configuration", "@type": "Specification", "filePath": "../docs/tech-specs/process/agent-rules/claude.mdx", "title": "Claude/Anthropic Agent Configuration", "description": "Claude/Anthropic specific configuration for milestone implementation", "created": "2025-05-25", "updated": "2025-05-25", "version": "1.0.0", "status": "Active", "tags": ["agent-rules", "claude", "anthropic", "executable"], "authors": ["nitishMeh<PERSON><PERSON>"]}, {"@id": "spec----docs-tech-specs-process-agent-rules-copilot-mdx-github-copilot-agent-configuration", "@type": "Specification", "filePath": "../docs/tech-specs/process/agent-rules/copilot.mdx", "title": "GitHub Copilot Agent Configuration", "description": "GitHub Copilot specific configuration for milestone implementation", "created": "2025-05-25", "updated": "2025-05-25", "version": "1.0.0", "status": "Active", "tags": ["agent-rules", "copilot", "github", "executable"], "authors": ["nitishMeh<PERSON><PERSON>"]}, {"@id": "spec----docs-tech-specs-process-agent-rules-core-mdx-core-agent-rules", "@type": "Specification", "filePath": "../docs/tech-specs/process/agent-rules/core.mdx", "title": "Core Agent Rules", "description": "Universal executable rules for all AI software engineering agents", "created": "2025-05-25", "updated": "2025-05-25", "version": "1.0.0", "status": "Active", "tags": ["agent-rules", "core", "executable"], "authors": ["nitishMeh<PERSON><PERSON>"]}, {"@id": "spec----docs-tech-specs-process-agent-rules-cursor-mdx-cursor-agent-configuration", "@type": "Specification", "filePath": "../docs/tech-specs/process/agent-rules/cursor.mdx", "title": "Cursor Agent Configuration", "description": "Cursor AI specific configuration for milestone implementation", "created": "2025-05-25", "updated": "2025-05-25", "version": "1.0.0", "status": "Active", "tags": ["agent-rules", "cursor", "executable"], "authors": ["nitishMeh<PERSON><PERSON>"]}, {"@id": "spec----docs-tech-specs-process-agent-rules-custom-mdx-custom-agent-configuration-template", "@type": "Specification", "filePath": "../docs/tech-specs/process/agent-rules/custom.mdx", "title": "Custom Agent Configuration Template", "description": "Template for configuring custom AI agents for milestone implementation", "created": "2025-05-25", "updated": "2025-05-25", "version": "1.0.0", "status": "Active", "tags": ["agent-rules", "custom", "template", "executable"], "authors": ["nitishMeh<PERSON><PERSON>"]}, {"@id": "spec----docs-tech-specs-process-agent-rules-validation-mdx-agent-configuration-validation", "@type": "Specification", "filePath": "../docs/tech-specs/process/agent-rules/validation.mdx", "title": "Agent Configuration Validation", "description": "Tools and procedures for validating AI agent configurations", "created": "2025-05-25", "updated": "2025-05-25", "version": "1.0.0", "status": "Active", "tags": ["agent-rules", "validation", "testing"], "authors": ["nitishMeh<PERSON><PERSON>"]}, {"@id": "spec----docs-tech-specs-process-core-architectural-decisions-mdx-architectural-decision-process", "@type": "ArchitecturalDecision", "filePath": "../docs/tech-specs/process/core/architectural-decisions.mdx", "title": "Architectural Decision Process", "description": "Process for creating, reviewing, and managing Architectural Decision Records (ADRs)", "created": "2025-05-25", "updated": "2025-05-25", "version": "1.0.0", "status": "Active", "tags": ["process", "architecture", "decisions", "adr"], "authors": ["nitishMeh<PERSON><PERSON>"], "owner": ["architecture-team"]}, {"@id": "spec----docs-tech-specs-process-core-documentation-mdx-documentation-process", "@type": "Specification", "filePath": "../docs/tech-specs/process/core/documentation.mdx", "title": "Documentation Process", "description": "Documentation standards, validation requirements, and maintenance procedures", "created": "2025-05-25", "updated": "2025-05-25", "version": "1.0.0", "status": "Active", "tags": ["process", "documentation", "validation", "standards"], "authors": ["nitishMeh<PERSON><PERSON>"], "owner": ["documentation-team"]}, {"@id": "spec----docs-tech-specs-process-core-error-recovery-mdx-error-recovery---rollback-process", "@type": "Specification", "filePath": "../docs/tech-specs/process/core/error-recovery.mdx", "title": "Error Recovery & Rollback Process", "description": "Procedures for handling implementation failures, errors, and rollback scenarios", "created": "2025-05-25", "updated": "2025-05-25", "version": "1.0.0", "status": "Active", "tags": ["process", "error-recovery", "rollback", "incident-response"], "authors": ["nitishMeh<PERSON><PERSON>"], "owner": ["devops-team", "sre-team"]}, {"@id": "spec----docs-tech-specs-process-core-git-workflow-mdx-git-workflow-process", "@type": "Specification", "filePath": "../docs/tech-specs/process/core/git-workflow.mdx", "title": "Git Workflow Process", "description": "Branching strategies, commit standards, and release processes", "created": "2025-05-25", "updated": "2025-05-25", "version": "1.0.0", "status": "Active", "tags": ["process", "git", "workflow", "branching"], "authors": ["nitishMeh<PERSON><PERSON>"], "owner": ["development-team"]}, {"@id": "spec----docs-tech-specs-process-core-milestone-implementation-mdx-milestone-implementation-process", "@type": "Milestone", "filePath": "../docs/tech-specs/process/core/milestone-implementation.mdx", "title": "Milestone Implementation Process", "description": "Comprehensive process for executing milestone specifications", "created": "2025-05-25", "updated": "2025-05-25", "version": "1.0.0", "status": "Active", "tags": ["process", "milestone", "implementation"], "authors": ["nitishMeh<PERSON><PERSON>"], "owner": ["development-team"]}, {"@id": "spec----docs-tech-specs-process-core-quality-assurance-mdx-quality-assurance-process", "@type": "Specification", "filePath": "../docs/tech-specs/process/core/quality-assurance.mdx", "title": "Quality Assurance Process", "description": "Validation, testing, and review processes for ensuring high-quality deliverables", "created": "2025-05-25", "updated": "2025-05-25", "version": "1.0.0", "status": "Active", "tags": ["process", "quality", "testing", "validation"], "authors": ["nitishMeh<PERSON><PERSON>"], "owner": ["quality-assurance-team"]}, {"@id": "spec----docs-tech-specs-process-current-status-mdx-current-project-status", "@type": "Specification", "filePath": "../docs/tech-specs/process/current-status.mdx", "title": "Current Project Status", "description": "Current state of the WorkflowMapperAgent development and next steps", "created": "2025-01-26T00:00:00.000Z", "version": "1.0.0", "status": "Active", "tags": ["status", "roadmap", "current-state"], "authors": ["nitishMeh<PERSON><PERSON>"]}, {"@id": "spec----docs-tech-specs-process-migration-guide-mdx-agent-configuration-migration-guide", "@type": "Specification", "filePath": "../docs/tech-specs/process/migration-guide.mdx", "title": "Agent Configuration Migration Guide", "description": "Guide for migrating to the streamlined agent configuration system", "created": "2025-05-25", "updated": "2025-05-25", "version": "1.0.0", "status": "Active", "tags": ["migration", "agent-configuration", "guide"], "authors": ["nitishMeh<PERSON><PERSON>"]}, {"@id": "spec----docs-tech-specs-process-optimisation-results-optimisation-result-001-results-mdx-agent-configuration-validation-results", "@type": "Specification", "filePath": "../docs/tech-specs/process/optimisation-results/optimisation-result-001-results.mdx", "title": "Agent Configuration Validation Results", "description": "Comprehensive validation results of streamlined agent configuration system", "created": "2025-05-25", "updated": "2025-05-25", "version": "1.0.0", "status": "Completed", "tags": ["validation", "agent-configuration", "optimization", "results"], "authors": ["nitishMeh<PERSON><PERSON>"]}, {"@id": "spec----docs-tech-specs-process-templates-adr-template-mdx-adr-xxx----decision-title-", "@type": "ArchitecturalDecision", "filePath": "../docs/tech-specs/process/templates/adr-template.mdx", "title": "ADR-XXX — <Decision Title>", "description": "<Brief description of the architectural decision>", "created": "<YYYY-MM-DD>", "updated": "<YYYY-MM-DD>", "version": "0.1.0", "status": "Proposed", "tags": ["adr", "architecture"], "authors": ["<author>"]}, {"@id": "spec----docs-tech-specs-process-templates-domain-template-mdx-domain-spec----domain-name-", "@type": "Domain", "filePath": "../docs/tech-specs/process/templates/domain-template.mdx", "title": "Domain Spec — <Domain Name>", "description": "<Brief description of the domain and its scope>", "created": "<YYYY-MM-DD>", "updated": "<YYYY-MM-DD>", "version": "0.0.0", "status": "Draft", "tags": ["domain", "<domain-tag>"], "authors": []}, {"@id": "spec----docs-tech-specs-process-templates-milestone-template-mdx-milestone--id-----one-line-scope-", "@type": "Milestone", "filePath": "../docs/tech-specs/process/templates/milestone-template.mdx", "title": "Milestone <ID> — <One-line scope>", "description": "<Short paragraph of intent>", "created": "<YYYY-MM-DD>", "version": "0.0.0", "status": "Draft", "tags": ["milestone"], "authors": []}, {"@id": "spec----docs-tech-specs-process-templates-process-improvement-mdx-process-improvement-template", "@type": "Specification", "filePath": "../docs/tech-specs/process/templates/process-improvement.mdx", "title": "Process Improvement Template", "description": "Streamlined template for capturing process improvements and lessons learned", "created": "2025-05-25", "updated": "2025-05-25", "version": "1.0.0", "status": "Active", "tags": ["template", "process-improvement", "lessons-learned", "agent-ready"], "authors": ["nitishMeh<PERSON><PERSON>"]}, {"@id": "spec----docs-tech-specs-process-templates-requirement-checklist-mdx-requirement-checklist-template", "@type": "Specification", "filePath": "../docs/tech-specs/process/templates/requirement-checklist.mdx", "title": "Requirement Checklist Template", "description": "Streamlined pre-implementation validation checklist", "created": "2025-05-25", "updated": "2025-05-25", "version": "1.0.0", "status": "Active", "tags": ["template", "checklist", "requirements", "agent-ready"], "authors": ["nitishMeh<PERSON><PERSON>"]}, {"@id": "spec----docs-tech-specs-process-templates-work-log-template-mdx-work-log-template", "@type": "Milestone", "filePath": "../docs/tech-specs/process/templates/work-log-template.mdx", "title": "Work Log Template", "description": "Streamlined template for milestone implementation work logs", "created": "2025-05-25", "updated": "2025-05-25", "version": "1.0.0", "status": "Active", "tags": ["template", "work-log", "milestone", "agent-ready"], "authors": ["nitishMeh<PERSON><PERSON>"]}, {"@id": "spec----docs-tech-specs-spec-checklist-mdx-spec-checklist", "@type": "Specification", "filePath": "../docs/tech-specs/spec_checklist.mdx", "title": "Spec Checklist", "description": "Quick reference for milestone specification validation requirements", "version": "2.1.0", "status": "Living", "tags": ["checklist", "validation", "reference"]}, {"@id": "spec----docs-tech-specs-structure-mdx-repository-structure---conventions", "@type": "Specification", "filePath": "../docs/tech-specs/structure.mdx", "title": "Repository Structure & Conventions", "description": "Living guideline—update with every structural PR. Single source of truth for project structure.", "created": "2025-05-25T00:00:00.000Z", "updated": "2025-05-25T00:00:00.000Z", "version": "0.2.0", "status": "Living", "tags": ["structure"], "authors": ["nitishMeh<PERSON><PERSON>"]}, {"@id": "function:fc43d08a", "@type": "function", "title": "anonymous", "description": "Function (()) => in packages/code-parser-lib/src/callGraph.test.ts", "filePath": "packages/code-parser-lib/src/callGraph.test.ts", "name": "anonymous", "signature": "(()) =>", "lang": "typescript", "line_start": 6, "line_end": 304}, {"@id": "function:47399227", "@type": "function", "title": "anonymous", "description": "Function (()) => in packages/code-parser-lib/src/callGraph.test.ts", "filePath": "packages/code-parser-lib/src/callGraph.test.ts", "name": "anonymous", "signature": "(()) =>", "lang": "typescript", "line_start": 9, "line_end": 11}, {"@id": "function:d50eb9c7", "@type": "function", "title": "anonymous", "description": "Function (()) => in packages/code-parser-lib/src/callGraph.test.ts", "filePath": "packages/code-parser-lib/src/callGraph.test.ts", "name": "anonymous", "signature": "(()) =>", "lang": "typescript", "line_start": 13, "line_end": 15}, {"@id": "function:a61dc44b", "@type": "function", "title": "anonymous", "description": "Function (()) => in packages/code-parser-lib/src/callGraph.test.ts", "filePath": "packages/code-parser-lib/src/callGraph.test.ts", "name": "anonymous", "signature": "(()) =>", "lang": "typescript", "line_start": 17, "line_end": 119}, {"@id": "function:43b5ccbe", "@type": "function", "title": "anonymous", "description": "Function (()) => in packages/code-parser-lib/src/callGraph.test.ts", "filePath": "packages/code-parser-lib/src/callGraph.test.ts", "name": "anonymous", "signature": "(()) =>", "lang": "typescript", "line_start": 18, "line_end": 52}, {"@id": "function:74d39373", "@type": "function", "title": "anonymous", "description": "Function (()) => in packages/code-parser-lib/src/callGraph.test.ts", "filePath": "packages/code-parser-lib/src/callGraph.test.ts", "name": "anonymous", "signature": "(()) =>", "lang": "typescript", "line_start": 54, "line_end": 79}, {"@id": "function:40f46082", "@type": "function", "title": "anonymous", "description": "Function (()) => in packages/code-parser-lib/src/callGraph.test.ts", "filePath": "packages/code-parser-lib/src/callGraph.test.ts", "name": "anonymous", "signature": "(()) =>", "lang": "typescript", "line_start": 81, "line_end": 102}, {"@id": "function:a6de02fc", "@type": "function", "title": "anonymous", "description": "Function (()) => in packages/code-parser-lib/src/callGraph.test.ts", "filePath": "packages/code-parser-lib/src/callGraph.test.ts", "name": "anonymous", "signature": "(()) =>", "lang": "typescript", "line_start": 104, "line_end": 118}, {"@id": "function:88e195b4", "@type": "function", "title": "anonymous", "description": "Function (()) => in packages/code-parser-lib/src/callGraph.test.ts", "filePath": "packages/code-parser-lib/src/callGraph.test.ts", "name": "anonymous", "signature": "(()) =>", "lang": "typescript", "line_start": 121, "line_end": 199}, {"@id": "function:73400a05", "@type": "function", "title": "anonymous", "description": "Function (()) => in packages/code-parser-lib/src/callGraph.test.ts", "filePath": "packages/code-parser-lib/src/callGraph.test.ts", "name": "anonymous", "signature": "(()) =>", "lang": "typescript", "line_start": 122, "line_end": 171}, {"@id": "function:61022add", "@type": "function", "title": "anonymous", "description": "Function (()) => in packages/code-parser-lib/src/callGraph.test.ts", "filePath": "packages/code-parser-lib/src/callGraph.test.ts", "name": "anonymous", "signature": "(()) =>", "lang": "typescript", "line_start": 173, "line_end": 198}, {"@id": "function:ea29626a", "@type": "function", "title": "anonymous", "description": "Function (()) => in packages/code-parser-lib/src/callGraph.test.ts", "filePath": "packages/code-parser-lib/src/callGraph.test.ts", "name": "anonymous", "signature": "(()) =>", "lang": "typescript", "line_start": 201, "line_end": 240}, {"@id": "function:31039cbf", "@type": "function", "title": "anonymous", "description": "Function (()) => in packages/code-parser-lib/src/callGraph.test.ts", "filePath": "packages/code-parser-lib/src/callGraph.test.ts", "name": "anonymous", "signature": "(()) =>", "lang": "typescript", "line_start": 202, "line_end": 239}, {"@id": "function:05d8ee50", "@type": "function", "title": "anonymous", "description": "Function (()) => in packages/code-parser-lib/src/callGraph.test.ts", "filePath": "packages/code-parser-lib/src/callGraph.test.ts", "name": "anonymous", "signature": "(()) =>", "lang": "typescript", "line_start": 242, "line_end": 303}, {"@id": "function:ce0994a2", "@type": "function", "title": "anonymous", "description": "Function (()) => in packages/code-parser-lib/src/callGraph.test.ts", "filePath": "packages/code-parser-lib/src/callGraph.test.ts", "name": "anonymous", "signature": "(()) =>", "lang": "typescript", "line_start": 243, "line_end": 282}, {"@id": "function:4098fc0d", "@type": "function", "title": "anonymous", "description": "Function (()) => in packages/code-parser-lib/src/callGraph.test.ts", "filePath": "packages/code-parser-lib/src/callGraph.test.ts", "name": "anonymous", "signature": "(()) =>", "lang": "typescript", "line_start": 284, "line_end": 302}, {"@id": "function:97d63265", "@type": "function", "title": "extractCallGraph", "description": "Function extractCallGraph(parseResult: CodeParseResult) in packages/code-parser-lib/src/callGraph.ts", "filePath": "packages/code-parser-lib/src/callGraph.ts", "name": "extractCallGraph", "signature": "extractCallGraph(parseResult: CodeParseResult)", "lang": "typescript", "line_start": 28, "line_end": 77}, {"@id": "function:65192c28", "@type": "function", "title": "anonymous", "description": "Function (target) => in packages/code-parser-lib/src/callGraph.ts", "filePath": "packages/code-parser-lib/src/callGraph.ts", "name": "anonymous", "signature": "(target) =>", "lang": "typescript", "line_start": 54, "line_end": 54}, {"@id": "function:fd3075cb", "@type": "function", "title": "findContainingFunction", "description": "Function findContainingFunction(call: ParsedCall, functions: ParsedFunction[]) in packages/code-parser-lib/src/callGraph.ts", "filePath": "packages/code-parser-lib/src/callGraph.ts", "name": "findContainingFunction", "signature": "findContainingFunction(call: Parsed<PERSON>all, functions: ParsedFunction[])", "lang": "typescript", "line_start": 82, "line_end": 94}, {"@id": "function:a8819230", "@type": "function", "title": "anonymous", "description": "Function (func) => in packages/code-parser-lib/src/callGraph.ts", "filePath": "packages/code-parser-lib/src/callGraph.ts", "name": "anonymous", "signature": "(func) =>", "lang": "typescript", "line_start": 84, "line_end": 84}, {"@id": "function:384f6d58", "@type": "function", "title": "mergeCallGraphs", "description": "Function mergeCallGraphs(results: CallGraphResult[]) in packages/code-parser-lib/src/callGraph.ts", "filePath": "packages/code-parser-lib/src/callGraph.ts", "name": "mergeCallGraphs", "signature": "mergeCallGraphs(results: CallGraphResult[])", "lang": "typescript", "line_start": 99, "line_end": 124}, {"@id": "function:f5c4af8d", "@type": "function", "title": "deduplicateEdges", "description": "Function deduplicateEdges(edges: CallGraphEdge[]) in packages/code-parser-lib/src/callGraph.ts", "filePath": "packages/code-parser-lib/src/callGraph.ts", "name": "deduplicateEdges", "signature": "deduplicateEdges(edges: CallGraphEdge[])", "lang": "typescript", "line_start": 129, "line_end": 143}, {"@id": "function:aa4e4aa2", "@type": "function", "title": "filterByConfidence", "description": "Function filterByConfidence(result: CallGraphResult, minConfidence: number) in packages/code-parser-lib/src/callGraph.ts", "filePath": "packages/code-parser-lib/src/callGraph.ts", "name": "filterByConfidence", "signature": "filterByConfidence(result: CallGraphResult, minConfidence: number)", "lang": "typescript", "line_start": 148, "line_end": 158}, {"@id": "function:3a1f1799", "@type": "function", "title": "anonymous", "description": "Function (edge) => in packages/code-parser-lib/src/callGraph.ts", "filePath": "packages/code-parser-lib/src/callGraph.ts", "name": "anonymous", "signature": "(edge) =>", "lang": "typescript", "line_start": 149, "line_end": 149}, {"@id": "function:8cdef9a6", "@type": "function", "title": "getCallGraphStats", "description": "Function getCallGraphStats(result: CallGraphResult) in packages/code-parser-lib/src/callGraph.ts", "filePath": "packages/code-parser-lib/src/callGraph.ts", "name": "getCallGraphStats", "signature": "getCallGraphStats(result: CallGraphResult)", "lang": "typescript", "line_start": 163, "line_end": 195}, {"@id": "function:763f85bc", "@type": "function", "title": "anonymous", "description": "Function ((sum, edge)) => in packages/code-parser-lib/src/callGraph.ts", "filePath": "packages/code-parser-lib/src/callGraph.ts", "name": "anonymous", "signature": "((sum, edge)) =>", "lang": "typescript", "line_start": 183, "line_end": 183}, {"@id": "function:048d506f", "@type": "function", "title": "anonymous", "description": "Function (edge) => in packages/code-parser-lib/src/callGraph.ts", "filePath": "packages/code-parser-lib/src/callGraph.ts", "name": "anonymous", "signature": "(edge) =>", "lang": "typescript", "line_start": 184, "line_end": 184}, {"@id": "function:84c3801e", "@type": "function", "title": "anonymous", "description": "Function (edge) => in packages/code-parser-lib/src/callGraph.ts", "filePath": "packages/code-parser-lib/src/callGraph.ts", "name": "anonymous", "signature": "(edge) =>", "lang": "typescript", "line_start": 185, "line_end": 185}, {"@id": "function:b669c9e2", "@type": "function", "title": "anonymous", "description": "Function (edge) => in packages/code-parser-lib/src/callGraph.ts", "filePath": "packages/code-parser-lib/src/callGraph.ts", "name": "anonymous", "signature": "(edge) =>", "lang": "typescript", "line_start": 186, "line_end": 186}, {"@id": "function:5dfda513", "@type": "function", "title": "anonymous", "description": "Function (()) => in packages/code-parser-lib/src/parseFile.test.ts", "filePath": "packages/code-parser-lib/src/parseFile.test.ts", "name": "anonymous", "signature": "(()) =>", "lang": "typescript", "line_start": 5, "line_end": 238}, {"@id": "function:46c27bff", "@type": "function", "title": "anonymous", "description": "Function (()) => in packages/code-parser-lib/src/parseFile.test.ts", "filePath": "packages/code-parser-lib/src/parseFile.test.ts", "name": "anonymous", "signature": "(()) =>", "lang": "typescript", "line_start": 8, "line_end": 11}, {"@id": "function:567fddf5", "@type": "function", "title": "anonymous", "description": "Function (()) => in packages/code-parser-lib/src/parseFile.test.ts", "filePath": "packages/code-parser-lib/src/parseFile.test.ts", "name": "anonymous", "signature": "(()) =>", "lang": "typescript", "line_start": 13, "line_end": 16}, {"@id": "function:3d13243e", "@type": "function", "title": "anonymous", "description": "Function (()) => in packages/code-parser-lib/src/parseFile.test.ts", "filePath": "packages/code-parser-lib/src/parseFile.test.ts", "name": "anonymous", "signature": "(()) =>", "lang": "typescript", "line_start": 18, "line_end": 74}, {"@id": "function:2ec63985", "@type": "function", "title": "anonymous", "description": "Function (()) => in packages/code-parser-lib/src/parseFile.test.ts", "filePath": "packages/code-parser-lib/src/parseFile.test.ts", "name": "anonymous", "signature": "(()) =>", "lang": "typescript", "line_start": 19, "line_end": 50}, {"@id": "function:aad1065e", "@type": "function", "title": "anonymous", "description": "Function (f) => in packages/code-parser-lib/src/parseFile.test.ts", "filePath": "packages/code-parser-lib/src/parseFile.test.ts", "name": "anonymous", "signature": "(f) =>", "lang": "typescript", "line_start": 41, "line_end": 41}, {"@id": "function:da18e242", "@type": "function", "title": "anonymous", "description": "Function (f) => in packages/code-parser-lib/src/parseFile.test.ts", "filePath": "packages/code-parser-lib/src/parseFile.test.ts", "name": "anonymous", "signature": "(f) =>", "lang": "typescript", "line_start": 47, "line_end": 47}, {"@id": "function:adf3ef07", "@type": "function", "title": "anonymous", "description": "Function (()) => in packages/code-parser-lib/src/parseFile.test.ts", "filePath": "packages/code-parser-lib/src/parseFile.test.ts", "name": "anonymous", "signature": "(()) =>", "lang": "typescript", "line_start": 52, "line_end": 73}, {"@id": "function:be6f21b8", "@type": "function", "title": "anonymous", "description": "Function (c) => in packages/code-parser-lib/src/parseFile.test.ts", "filePath": "packages/code-parser-lib/src/parseFile.test.ts", "name": "anonymous", "signature": "(c) =>", "lang": "typescript", "line_start": 70, "line_end": 70}, {"@id": "function:c0f018f4", "@type": "function", "title": "anonymous", "description": "Function (()) => in packages/code-parser-lib/src/parseFile.test.ts", "filePath": "packages/code-parser-lib/src/parseFile.test.ts", "name": "anonymous", "signature": "(()) =>", "lang": "typescript", "line_start": 76, "line_end": 142}, {"@id": "function:664b8404", "@type": "function", "title": "anonymous", "description": "Function (()) => in packages/code-parser-lib/src/parseFile.test.ts", "filePath": "packages/code-parser-lib/src/parseFile.test.ts", "name": "anonymous", "signature": "(()) =>", "lang": "typescript", "line_start": 77, "line_end": 117}, {"@id": "function:6d85ff20", "@type": "function", "title": "anonymous", "description": "Function (f) => in packages/code-parser-lib/src/parseFile.test.ts", "filePath": "packages/code-parser-lib/src/parseFile.test.ts", "name": "anonymous", "signature": "(f) =>", "lang": "typescript", "line_start": 107, "line_end": 107}, {"@id": "function:ae726c8a", "@type": "function", "title": "anonymous", "description": "Function (f) => in packages/code-parser-lib/src/parseFile.test.ts", "filePath": "packages/code-parser-lib/src/parseFile.test.ts", "name": "anonymous", "signature": "(f) =>", "lang": "typescript", "line_start": 112, "line_end": 112}, {"@id": "function:5350533f", "@type": "function", "title": "anonymous", "description": "Function (f) => in packages/code-parser-lib/src/parseFile.test.ts", "filePath": "packages/code-parser-lib/src/parseFile.test.ts", "name": "anonymous", "signature": "(f) =>", "lang": "typescript", "line_start": 115, "line_end": 115}, {"@id": "function:74991f0f", "@type": "function", "title": "anonymous", "description": "Function (()) => in packages/code-parser-lib/src/parseFile.test.ts", "filePath": "packages/code-parser-lib/src/parseFile.test.ts", "name": "anonymous", "signature": "(()) =>", "lang": "typescript", "line_start": 119, "line_end": 141}, {"@id": "function:a055822f", "@type": "function", "title": "anonymous", "description": "Function (c) => in packages/code-parser-lib/src/parseFile.test.ts", "filePath": "packages/code-parser-lib/src/parseFile.test.ts", "name": "anonymous", "signature": "(c) =>", "lang": "typescript", "line_start": 139, "line_end": 139}, {"@id": "function:209f6397", "@type": "function", "title": "anonymous", "description": "Function (()) => in packages/code-parser-lib/src/parseFile.test.ts", "filePath": "packages/code-parser-lib/src/parseFile.test.ts", "name": "anonymous", "signature": "(()) =>", "lang": "typescript", "line_start": 144, "line_end": 167}, {"@id": "function:306a96b5", "@type": "function", "title": "anonymous", "description": "Function (()) => in packages/code-parser-lib/src/parseFile.test.ts", "filePath": "packages/code-parser-lib/src/parseFile.test.ts", "name": "anonymous", "signature": "(()) =>", "lang": "typescript", "line_start": 145, "line_end": 155}, {"@id": "function:9583dcb5", "@type": "function", "title": "anonymous", "description": "Function (()) => in packages/code-parser-lib/src/parseFile.test.ts", "filePath": "packages/code-parser-lib/src/parseFile.test.ts", "name": "anonymous", "signature": "(()) =>", "lang": "typescript", "line_start": 157, "line_end": 166}, {"@id": "function:ecf89729", "@type": "function", "title": "anonymous", "description": "Function (()) => in packages/code-parser-lib/src/parseFile.test.ts", "filePath": "packages/code-parser-lib/src/parseFile.test.ts", "name": "anonymous", "signature": "(()) =>", "lang": "typescript", "line_start": 169, "line_end": 220}, {"@id": "function:91bcd60f", "@type": "function", "title": "anonymous", "description": "Function (()) => in packages/code-parser-lib/src/parseFile.test.ts", "filePath": "packages/code-parser-lib/src/parseFile.test.ts", "name": "anonymous", "signature": "(()) =>", "lang": "typescript", "line_start": 170, "line_end": 196}, {"@id": "function:18cac84d", "@type": "function", "title": "anonymous", "description": "Function (f) => in packages/code-parser-lib/src/parseFile.test.ts", "filePath": "packages/code-parser-lib/src/parseFile.test.ts", "name": "anonymous", "signature": "(f) =>", "lang": "typescript", "line_start": 189, "line_end": 189}, {"@id": "function:d8bb749a", "@type": "function", "title": "anonymous", "description": "Function (f) => in packages/code-parser-lib/src/parseFile.test.ts", "filePath": "packages/code-parser-lib/src/parseFile.test.ts", "name": "anonymous", "signature": "(f) =>", "lang": "typescript", "line_start": 190, "line_end": 190}, {"@id": "function:394ee992", "@type": "function", "title": "anonymous", "description": "Function (()) => in packages/code-parser-lib/src/parseFile.test.ts", "filePath": "packages/code-parser-lib/src/parseFile.test.ts", "name": "anonymous", "signature": "(()) =>", "lang": "typescript", "line_start": 198, "line_end": 209}, {"@id": "function:b742c01b", "@type": "function", "title": "anonymous", "description": "Function (()) => in packages/code-parser-lib/src/parseFile.test.ts", "filePath": "packages/code-parser-lib/src/parseFile.test.ts", "name": "anonymous", "signature": "(()) =>", "lang": "typescript", "line_start": 211, "line_end": 219}, {"@id": "function:3a47a5d6", "@type": "function", "title": "anonymous", "description": "Function (()) => in packages/code-parser-lib/src/parseFile.test.ts", "filePath": "packages/code-parser-lib/src/parseFile.test.ts", "name": "anonymous", "signature": "(()) =>", "lang": "typescript", "line_start": 222, "line_end": 237}, {"@id": "function:698732c9", "@type": "function", "title": "anonymous", "description": "Function (()) => in packages/code-parser-lib/src/parseFile.test.ts", "filePath": "packages/code-parser-lib/src/parseFile.test.ts", "name": "anonymous", "signature": "(()) =>", "lang": "typescript", "line_start": 223, "line_end": 236}, {"@id": "function:9f4c9a86", "@type": "function", "title": "parseCodeDirectory", "description": "Function parseCodeDirectory(directoryPath: string, languages: string[] = ['py', 'js']) in packages/code-parser-lib/src/parseFile.ts", "filePath": "packages/code-parser-lib/src/parseFile.ts", "name": "parseCodeDirectory", "signature": "parseCodeDirectory(directoryPath: string, languages: string[] = ['py', 'js'])", "lang": "typescript", "line_start": 44, "line_end": 75}, {"@id": "function:c9a3b8d4", "@type": "function", "title": "parseFile", "description": "Function parseFile(filePath: string) in packages/code-parser-lib/src/parseFile.ts", "filePath": "packages/code-parser-lib/src/parseFile.ts", "name": "parseFile", "signature": "parseFile(filePath: string)", "lang": "typescript", "line_start": 80, "line_end": 135}, {"@id": "function:271d9d19", "@type": "function", "title": "findCodeFiles", "description": "Function findCodeFiles(directoryPath: string, languages: string[]) in packages/code-parser-lib/src/parseFile.ts", "filePath": "packages/code-parser-lib/src/parseFile.ts", "name": "findCodeFiles", "signature": "findCodeFiles(directoryPath: string, languages: string[])", "lang": "typescript", "line_start": 140, "line_end": 174}, {"@id": "function:1dba8e5c", "@type": "function", "title": "anonymous", "description": "Function (lang) => in packages/code-parser-lib/src/parseFile.ts", "filePath": "packages/code-parser-lib/src/parseFile.ts", "name": "anonymous", "signature": "(lang) =>", "lang": "typescript", "line_start": 142, "line_end": 149}, {"@id": "function:6478eff2", "@type": "function", "title": "traverse", "description": "Function traverse(dir: string) in packages/code-parser-lib/src/parseFile.ts", "filePath": "packages/code-parser-lib/src/parseFile.ts", "name": "traverse", "signature": "traverse(dir: string)", "lang": "typescript", "line_start": 151, "line_end": 170}, {"@id": "function:3b49bdbd", "@type": "function", "title": "getLanguageFromFile", "description": "Function getLanguageFromFile(filePath: string) in packages/code-parser-lib/src/parseFile.ts", "filePath": "packages/code-parser-lib/src/parseFile.ts", "name": "getLanguageFromFile", "signature": "getLanguageFromFile(filePath: string)", "lang": "typescript", "line_start": 179, "line_end": 195}, {"@id": "function:409648f3", "@type": "function", "title": "extractFunctions", "description": "Function extractFunctions(node: Parser.SyntaxNode, filePath: string, lang: 'python' | 'javascript' | 'typescript', content: string) in packages/code-parser-lib/src/parseFile.ts", "filePath": "packages/code-parser-lib/src/parseFile.ts", "name": "extractFunctions", "signature": "extractFunctions(node: Parser.SyntaxNode, filePath: string, lang: 'python' | 'javascript' | 'typescript', content: string)", "lang": "typescript", "line_start": 200, "line_end": 330}, {"@id": "function:c8b6a0c8", "@type": "function", "title": "traverse", "description": "Function traverse(node: Parser.SyntaxNode) in packages/code-parser-lib/src/parseFile.ts", "filePath": "packages/code-parser-lib/src/parseFile.ts", "name": "traverse", "signature": "traverse(node: Parser.SyntaxNode)", "lang": "typescript", "line_start": 203, "line_end": 326}, {"@id": "function:2ff01b37", "@type": "function", "title": "extractCalls", "description": "Function extractCalls(node: Parser.SyntaxNode, filePath: string, content: string) in packages/code-parser-lib/src/parseFile.ts", "filePath": "packages/code-parser-lib/src/parseFile.ts", "name": "extractCalls", "signature": "extractCalls(node: Parser.SyntaxNode, filePath: string, content: string)", "lang": "typescript", "line_start": 335, "line_end": 366}, {"@id": "function:ffa8d3bd", "@type": "function", "title": "traverse", "description": "Function traverse(node: Parser.SyntaxNode) in packages/code-parser-lib/src/parseFile.ts", "filePath": "packages/code-parser-lib/src/parseFile.ts", "name": "traverse", "signature": "traverse(node: Parser.SyntaxNode)", "lang": "typescript", "line_start": 338, "line_end": 362}, {"@id": "function:0b715780", "@type": "function", "title": "generateFunctionId", "description": "Function generateFunctionId(filePath: string, name: string, startLine: number) in packages/code-parser-lib/src/parseFile.ts", "filePath": "packages/code-parser-lib/src/parseFile.ts", "name": "generateFunctionId", "signature": "generateFunctionId(filePath: string, name: string, startLine: number)", "lang": "typescript", "line_start": 371, "line_end": 378}, {"@type": "workflow_calls", "source": "function:97d63265", "target": "function:fd3075cb", "relationship": "calls", "call_type": "direct", "confidence": 1, "file": "packages/code-parser-lib/src/callGraph.ts", "line": 49}, {"@type": "workflow_calls", "source": "function:384f6d58", "target": "function:f5c4af8d", "relationship": "calls", "call_type": "direct", "confidence": 1, "file": "packages/code-parser-lib/src/callGraph.ts", "line": 117}, {"@type": "workflow_calls", "source": "function:9f4c9a86", "target": "function:271d9d19", "relationship": "calls", "call_type": "direct", "confidence": 1, "file": "packages/code-parser-lib/src/parseFile.ts", "line": 50}, {"@type": "workflow_calls", "source": "function:9f4c9a86", "target": "function:c9a3b8d4", "relationship": "calls", "call_type": "direct", "confidence": 1, "file": "packages/code-parser-lib/src/parseFile.ts", "line": 54}, {"@type": "workflow_calls", "source": "function:c9a3b8d4", "target": "function:3b49bdbd", "relationship": "calls", "call_type": "direct", "confidence": 1, "file": "packages/code-parser-lib/src/parseFile.ts", "line": 87}, {"@type": "workflow_calls", "source": "function:c9a3b8d4", "target": "function:409648f3", "relationship": "calls", "call_type": "direct", "confidence": 1, "file": "packages/code-parser-lib/src/parseFile.ts", "line": 119}, {"@type": "workflow_calls", "source": "function:c9a3b8d4", "target": "function:2ff01b37", "relationship": "calls", "call_type": "direct", "confidence": 1, "file": "packages/code-parser-lib/src/parseFile.ts", "line": 123}, {"@type": "workflow_calls", "source": "function:271d9d19", "target": "function:6478eff2", "relationship": "calls", "call_type": "direct", "confidence": 1, "file": "packages/code-parser-lib/src/parseFile.ts", "line": 161}, {"@type": "workflow_calls", "source": "function:271d9d19", "target": "function:c8b6a0c8", "relationship": "calls", "call_type": "direct", "confidence": 1, "file": "packages/code-parser-lib/src/parseFile.ts", "line": 161}, {"@type": "workflow_calls", "source": "function:271d9d19", "target": "function:ffa8d3bd", "relationship": "calls", "call_type": "direct", "confidence": 1, "file": "packages/code-parser-lib/src/parseFile.ts", "line": 161}, {"@type": "workflow_calls", "source": "function:271d9d19", "target": "function:6478eff2", "relationship": "calls", "call_type": "direct", "confidence": 1, "file": "packages/code-parser-lib/src/parseFile.ts", "line": 172}, {"@type": "workflow_calls", "source": "function:271d9d19", "target": "function:c8b6a0c8", "relationship": "calls", "call_type": "direct", "confidence": 1, "file": "packages/code-parser-lib/src/parseFile.ts", "line": 172}, {"@type": "workflow_calls", "source": "function:271d9d19", "target": "function:ffa8d3bd", "relationship": "calls", "call_type": "direct", "confidence": 1, "file": "packages/code-parser-lib/src/parseFile.ts", "line": 172}, {"@type": "workflow_calls", "source": "function:409648f3", "target": "function:0b715780", "relationship": "calls", "call_type": "direct", "confidence": 1, "file": "packages/code-parser-lib/src/parseFile.ts", "line": 215}, {"@type": "workflow_calls", "source": "function:409648f3", "target": "function:0b715780", "relationship": "calls", "call_type": "direct", "confidence": 1, "file": "packages/code-parser-lib/src/parseFile.ts", "line": 262}, {"@type": "workflow_calls", "source": "function:409648f3", "target": "function:0b715780", "relationship": "calls", "call_type": "direct", "confidence": 1, "file": "packages/code-parser-lib/src/parseFile.ts", "line": 309}, {"@type": "workflow_calls", "source": "function:409648f3", "target": "function:6478eff2", "relationship": "calls", "call_type": "direct", "confidence": 1, "file": "packages/code-parser-lib/src/parseFile.ts", "line": 324}, {"@type": "workflow_calls", "source": "function:409648f3", "target": "function:c8b6a0c8", "relationship": "calls", "call_type": "direct", "confidence": 1, "file": "packages/code-parser-lib/src/parseFile.ts", "line": 324}, {"@type": "workflow_calls", "source": "function:409648f3", "target": "function:ffa8d3bd", "relationship": "calls", "call_type": "direct", "confidence": 1, "file": "packages/code-parser-lib/src/parseFile.ts", "line": 324}, {"@type": "workflow_calls", "source": "function:409648f3", "target": "function:6478eff2", "relationship": "calls", "call_type": "direct", "confidence": 1, "file": "packages/code-parser-lib/src/parseFile.ts", "line": 328}, {"@type": "workflow_calls", "source": "function:409648f3", "target": "function:c8b6a0c8", "relationship": "calls", "call_type": "direct", "confidence": 1, "file": "packages/code-parser-lib/src/parseFile.ts", "line": 328}, {"@type": "workflow_calls", "source": "function:409648f3", "target": "function:ffa8d3bd", "relationship": "calls", "call_type": "direct", "confidence": 1, "file": "packages/code-parser-lib/src/parseFile.ts", "line": 328}, {"@type": "workflow_calls", "source": "function:2ff01b37", "target": "function:6478eff2", "relationship": "calls", "call_type": "direct", "confidence": 1, "file": "packages/code-parser-lib/src/parseFile.ts", "line": 360}, {"@type": "workflow_calls", "source": "function:2ff01b37", "target": "function:c8b6a0c8", "relationship": "calls", "call_type": "direct", "confidence": 1, "file": "packages/code-parser-lib/src/parseFile.ts", "line": 360}, {"@type": "workflow_calls", "source": "function:2ff01b37", "target": "function:ffa8d3bd", "relationship": "calls", "call_type": "direct", "confidence": 1, "file": "packages/code-parser-lib/src/parseFile.ts", "line": 360}, {"@type": "workflow_calls", "source": "function:2ff01b37", "target": "function:6478eff2", "relationship": "calls", "call_type": "direct", "confidence": 1, "file": "packages/code-parser-lib/src/parseFile.ts", "line": 364}, {"@type": "workflow_calls", "source": "function:2ff01b37", "target": "function:c8b6a0c8", "relationship": "calls", "call_type": "direct", "confidence": 1, "file": "packages/code-parser-lib/src/parseFile.ts", "line": 364}, {"@type": "workflow_calls", "source": "function:2ff01b37", "target": "function:ffa8d3bd", "relationship": "calls", "call_type": "direct", "confidence": 1, "file": "packages/code-parser-lib/src/parseFile.ts", "line": 364}]}