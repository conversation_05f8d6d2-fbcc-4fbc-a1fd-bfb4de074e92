/**
 * @fileoverview Integration tests for Task 13 - End-to-end CLI and workflow scenarios
 * Tests the complete sync-kg workflow from git diff to knowledge graph generation
 */

import { execSync } from 'child_process';
import { existsSync, writeFileSync, readFileSync, mkdirSync, rmSync } from 'fs';
import { join, resolve } from 'path';
import { tmpdir } from 'os';
import { simpleGit } from 'simple-git';

describe('Integration Tests - Task 13', () => {
  let testRepoDir: string;
  let specsDir: string;
  let git: any;

  beforeAll(async () => {
    // Create temporary test repository
    testRepoDir = join(tmpdir(), `kg-sync-integration-${Date.now()}`);
    specsDir = join(testRepoDir, 'specs');

    mkdirSync(testRepoDir, { recursive: true });
    mkdirSync(specsDir, { recursive: true });

    // Initialize git repository
    git = simpleGit(testRepoDir);
    await git.init();
    await git.addConfig('user.name', 'Test User');
    await git.addConfig('user.email', '<EMAIL>');

    // Create initial milestone specification
    const milestoneSpec = `---
title: Test Milestone M1.2
description: Integration test milestone
---

# Test Milestone M1.2

## Task Breakdown

### Task 01: TestComponent
- Implement test functionality

### Task 02: AuthService
- Implement authentication service

## Deliverables

| Component | Description |
|-----------|-------------|
| TestComponent | Test component implementation |
| AuthService | Authentication service |
`;

    writeFileSync(join(specsDir, 'milestone-m1.2.mdx'), milestoneSpec);

    // Create initial commit
    await git.add('.');
    await git.commit('Initial commit with milestone spec');
  });

  afterAll(() => {
    // Clean up test repository
    if (existsSync(testRepoDir)) {
      rmSync(testRepoDir, { recursive: true, force: true });
    }
  });

  describe('1️⃣ Annotation Parse Integration', () => {
    it('should parse @implements annotations correctly', async () => {
      // Test the parseAnnotations function directly as specified in acceptance test
      const { parseAnnotations } = await import('@workflow-mapper/kg-sync-lib');

      const testCode =
        '/** @implements milestone-M1.2#TestComponent */\nfunction testFunction() {}';
      const result = parseAnnotations(testCode, 'test.ts');

      expect(result).toHaveLength(1);
      expect(result[0]).toMatchObject({
        milestoneId: 'M1.2',
        componentName: 'TestComponent',
        functionName: 'testFunction',
      });
    });

    it('should handle multiple annotations in a file', async () => {
      const { parseAnnotations } = await import('@workflow-mapper/kg-sync-lib');

      const testCode = `
/** @implements milestone-M1.2#TestComponent */
function testFunction() {}

/** @implements milestone-M1.2#AuthService */
class AuthService {
  authenticate() {}
}
`;

      const result = parseAnnotations(testCode, 'test.ts');

      expect(result).toHaveLength(2);
      expect(result[0]?.componentName).toBe('TestComponent');
      expect(result[1]?.componentName).toBe('AuthService');
    });
  });

  describe('2️⃣ Graph Update Integration', () => {
    it('should update knowledge graph when adding annotated function', async () => {
      // Create a new file with annotation
      const testFile = join(testRepoDir, 'src', 'test.ts');
      mkdirSync(join(testRepoDir, 'src'), { recursive: true });

      const annotatedCode = `
/**
 * @implements milestone-M1.2#TestComponent
 */
function testFunction() {
  return 'test implementation';
}
`;

      writeFileSync(testFile, annotatedCode);
      await git.add('.');
      await git.commit('Add annotated test function');

      // Run sync-kg command
      const cliPath = resolve(__dirname, '../dist/sync-kg.js');
      let result: string;
      try {
        result = execSync(
          `node "${cliPath}" "${specsDir}" --since HEAD~1 --output-dir "${testRepoDir}"`,
          {
            cwd: testRepoDir,
            encoding: 'utf-8',
            stdio: 'pipe',
          }
        );
      } catch (error: any) {
        // If command fails, we still want to check the output
        result = error.stdout?.toString() || '';
        if (error.status !== 0) {
          console.log('CLI Error:', error.status, result);
          throw error;
        }
      }

      // Verify knowledge graph was created
      expect(existsSync(join(testRepoDir, 'kg.jsonld'))).toBe(true);
      expect(existsSync(join(testRepoDir, 'kg.yaml'))).toBe(true);

      // Verify implements edge was created
      const kgContent = readFileSync(join(testRepoDir, 'kg.jsonld'), 'utf-8');
      const kg = JSON.parse(kgContent);

      const implementsEdges = kg['@graph'].filter(
        (item: any) => item['@type'] === 'implements'
      );
      expect(implementsEdges).toHaveLength(1);
      expect(implementsEdges[0].target).toContain('TestComponent');

      // Verify CLI output indicates success
      expect(result).toContain('Sync completed');
      expect(result).toContain('✅');
    });

    it('should handle incremental updates correctly', async () => {
      // Add another annotated function
      const testFile2 = join(testRepoDir, 'src', 'auth.ts');
      const annotatedCode2 = `
/**
 * @implements milestone-M1.2#AuthService
 */
export class AuthService {
  authenticate(token: string): boolean {
    return token === 'valid';
  }
}
`;

      writeFileSync(testFile2, annotatedCode2);
      await git.add('.');
      await git.commit('Add AuthService implementation');

      // Run sync-kg again
      const cliPath = resolve(__dirname, '../dist/sync-kg.js');
      execSync(
        `node "${cliPath}" "${specsDir}" --since HEAD~1 --output-dir "${testRepoDir}"`,
        {
          cwd: testRepoDir,
          encoding: 'utf-8',
          stdio: 'pipe',
        }
      );

      // Verify both implements edges exist
      const kgContent = readFileSync(join(testRepoDir, 'kg.jsonld'), 'utf-8');
      const kg = JSON.parse(kgContent);

      const implementsEdges = kg['@graph'].filter(
        (item: any) => item['@type'] === 'implements'
      );
      expect(implementsEdges).toHaveLength(2);

      const componentNames = implementsEdges.map(
        (edge: any) => edge.target.split('#')[1]
      );
      expect(componentNames).toContain('TestComponent');
      expect(componentNames).toContain('AuthService');
    });
  });

  describe('3️⃣ Coverage Threshold Integration', () => {
    it('should exit 0 when coverage is above threshold', async () => {
      // Both components are implemented, coverage should be 100%
      const cliPath = resolve(__dirname, '../dist/sync-kg.js');

      const result = execSync(
        `node "${cliPath}" "${specsDir}" --since HEAD~2 --threshold 0.5 --output-dir "${testRepoDir}"`,
        {
          cwd: testRepoDir,
          encoding: 'utf-8',
          stdio: 'pipe',
        }
      );

      expect(result).toContain('✅ Sync completed successfully');
      expect(result).toContain('100.0%'); // Should show 100% coverage
    });

    it('should exit 60 when coverage drops below threshold', async () => {
      // Remove one annotation to drop coverage below 50%
      const testFile = join(testRepoDir, 'src', 'test.ts');
      const codeWithoutAnnotation = `
// Annotation removed - this should cause coverage drop
function testFunction() {
  return 'test implementation';
}
`;

      writeFileSync(testFile, codeWithoutAnnotation);
      await git.add('.');
      await git.commit('Remove annotation to test coverage threshold');

      const cliPath = resolve(__dirname, '../dist/sync-kg.js');

      try {
        execSync(
          `node "${cliPath}" "${specsDir}" --since HEAD~1 --threshold 0.8 --output-dir "${testRepoDir}"`,
          {
            cwd: testRepoDir,
            stdio: 'pipe',
            encoding: 'utf-8',
          }
        );

        // Should not reach here - command should exit with code 60
        throw new Error('Command should exit with code 60 for coverage breach');
      } catch (error: any) {
        expect(error.status).toBe(60);
        const stdout = error.stdout?.toString() || '';
        expect(stdout).toContain('❌ Coverage threshold breach detected');
      }
    });
  });

  describe('4️⃣ CLI Workflow Integration', () => {
    it('should handle parse errors with exit code 70', async () => {
      // Create file with malformed annotation
      const testFile = join(testRepoDir, 'src', 'malformed.ts');
      const malformedCode = `
/**
 * @implements invalid-format
 */
function malformedFunction() {}
`;

      writeFileSync(testFile, malformedCode);
      await git.add('.');
      await git.commit('Add malformed annotation');

      const cliPath = resolve(__dirname, '../dist/sync-kg.js');

      try {
        execSync(
          `node "${cliPath}" "${specsDir}" --since HEAD~1 --output-dir "${testRepoDir}"`,
          {
            cwd: testRepoDir,
            stdio: 'pipe',
            encoding: 'utf-8',
          }
        );

        throw new Error('Command should exit with code 70 for parse errors');
      } catch (error: any) {
        expect(error.status).toBe(70);
        const stdout = error.stdout?.toString() || '';
        expect(stdout).toContain('❌ Annotation parse errors detected');
        expect(stdout).toContain('parse error(s) found');
      }
    });

    it('should handle dry-run mode correctly', async () => {
      const cliPath = resolve(__dirname, '../dist/sync-kg.js');

      // Remove existing output files
      if (existsSync(join(testRepoDir, 'kg.jsonld'))) {
        rmSync(join(testRepoDir, 'kg.jsonld'));
      }
      if (existsSync(join(testRepoDir, 'kg.yaml'))) {
        rmSync(join(testRepoDir, 'kg.yaml'));
      }

      const result = execSync(
        `node "${cliPath}" "${specsDir}" --since HEAD~1 --dry-run --output-dir "${testRepoDir}"`,
        {
          cwd: testRepoDir,
          encoding: 'utf-8',
          stdio: 'pipe',
        }
      );

      // Verify dry-run messages
      expect(result).toContain('Dry run: Knowledge graph would be saved');
      expect(result).toContain('Dry run: Changes report would be saved');

      // Verify files were NOT created in dry-run mode
      expect(existsSync(join(testRepoDir, 'kg.jsonld'))).toBe(false);
      expect(existsSync(join(testRepoDir, 'kg.yaml'))).toBe(false);
    });

    it('should display performance metrics', async () => {
      const cliPath = resolve(__dirname, '../dist/sync-kg.js');

      const result = execSync(
        `node "${cliPath}" "${specsDir}" --since HEAD~1 --output-dir "${testRepoDir}"`,
        {
          cwd: testRepoDir,
          encoding: 'utf-8',
          stdio: 'pipe',
        }
      );

      // Verify performance metrics are displayed
      expect(result).toContain('📈 Performance:');
      expect(result).toContain('files processed');
      expect(result).toContain('annotations found');
      expect(result).toMatch(/Sync completed in \d+ms/);
    });

    it('should handle edge cases gracefully', async () => {
      // Test with no changes
      const cliPath = resolve(__dirname, '../dist/sync-kg.js');

      const result = execSync(
        `node "${cliPath}" "${specsDir}" --since HEAD --output-dir "${testRepoDir}"`,
        {
          cwd: testRepoDir,
          encoding: 'utf-8',
          stdio: 'pipe',
        }
      );

      expect(result).toContain('No changes detected');
      expect(result).toContain('knowledge graph is up to date');
    });
  });
});
