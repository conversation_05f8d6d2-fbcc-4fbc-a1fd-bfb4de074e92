# @workflow-mapper/kg-sync-lib

Bidirectional sync library for knowledge graph incremental updates.

## Overview

This package provides functionality for:
- Parsing `@implements` annotations from source code
- Detecting changed files using git diff
- Incrementally updating knowledge graphs
- Calculating confidence scores and coverage metrics
- Bidirectional synchronization between code and specifications

## Installation

```bash
pnpm add @workflow-mapper/kg-sync-lib
```

## Usage

```typescript
import { syncKnowledgeGraph } from '@workflow-mapper/kg-sync-lib';

const result = await syncKnowledgeGraph('./docs/tech-specs', {
  since: 'origin/main',
  dryRun: false,
  outputDir: '.',
  verbose: true
});

console.log(`Coverage: ${result.coverageMetrics.length} milestones processed`);
```

## API

### Core Functions

- `syncKnowledgeGraph(directory, options)` - Main sync orchestration
- `diffGit(options)` - Git diff detection
- `parseAnnotations(content, filePath)` - Annotation parsing
- `updateGraph(graph, annotations, files)` - Graph updates
- `extractComponents(content, milestoneId)` - Component extraction
- `calculateConfidence(annotation, exists)` - Confidence scoring
- `calculateCoverage(milestoneId, edges, total)` - Coverage calculation

### Types

See `src/types.ts` for complete type definitions.

## Development

```bash
# Build
pnpm build

# Test
pnpm test

# Type check
pnpm type-check
```

## License

ISC
