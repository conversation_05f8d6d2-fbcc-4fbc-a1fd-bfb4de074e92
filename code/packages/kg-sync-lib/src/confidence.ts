/**
 * @fileoverview Confidence scoring and coverage calculation
 * @implements milestone-M1.2#ConfidenceScoring
 */

import type {
  Annotation,
  MilestoneCoverage,
  KnowledgeGraphEdge,
} from './types.js';

/**
 * Calculate the number of days between two dates
 * @param dateString - ISO date string
 * @param now - Current date
 * @returns Number of days between dates
 */
function daysBetween(dateString: string, now: Date): number {
  const date = new Date(dateString);
  const diffTime = Math.abs(now.getTime() - date.getTime());
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
}

/**
 * Calculate confidence score for an annotation
 * @param annotation - Annotation to score
 * @param functionExists - Whether the function still exists
 * @returns Confidence score (0.0 to 1.0)
 */
export function calculateConfidence(
  annotation: Annotation,
  functionExists: boolean
): number {
  // If function doesn't exist, very low confidence (stale)
  if (!functionExists) {
    return 0.1;
  }

  // If annotation has errors, reduced confidence
  if (annotation.errors.some((e) => e.severity === 'error')) {
    return 0.5;
  }

  // Check how long since last verified
  const daysSinceVerified = daysBetween(annotation.lastVerified, new Date());

  // If not verified recently (>30 days), slightly reduced confidence
  if (daysSinceVerified > 30) {
    return 0.8;
  }

  // Valid annotation, function exists, recently verified
  return 1.0;
}

/**
 * Calculate milestone confidence from implements edges
 * @param implementsEdges - Array of implements edges for the milestone
 * @returns Average confidence score (0.0 to 1.0)
 */
export function calculateMilestoneConfidence(
  implementsEdges: KnowledgeGraphEdge[]
): number {
  if (implementsEdges.length === 0) {
    return 0.0;
  }

  const totalConfidence = implementsEdges.reduce(
    (sum, edge) => sum + edge.confidence,
    0
  );
  return totalConfidence / implementsEdges.length;
}

/**
 * Update confidence for stale edges
 * @param _edge - Edge to update (unused in current implementation)
 * @param staleReason - Reason for staleness
 * @returns Updated confidence score
 */
export function calculateStaleConfidence(
  _edge: KnowledgeGraphEdge,
  staleReason: string
): number {
  switch (staleReason) {
    case 'annotation_removed':
      // Annotation removed but function still exists
      return 0.2;
    case 'function_deleted':
      // Function no longer exists
      return 0.1;
    default:
      // Unknown stale reason, use conservative score
      return 0.1;
  }
}

/**
 * Extract component name from an edge target
 * @param edgeTarget - Edge target ID (e.g., "component:M1.2#ComponentName")
 * @returns Component name or null if invalid format
 */
function extractComponentNameFromEdge(edgeTarget: string): string | null {
  const match = edgeTarget.match(/^component:[^#]+#(.+)$/);
  return match && match[1] ? match[1] : null;
}

/**
 * Calculate effective coverage considering partial implementations
 * @param implementsEdges - Implements edges for the milestone
 * @param totalComponents - Total number of components
 * @returns Effective coverage score (0.0 to 1.0)
 */
function calculateEffectiveCoverage(
  implementsEdges: KnowledgeGraphEdge[],
  totalComponents: number
): number {
  if (totalComponents === 0) {
    return 1.0; // 100% coverage if no components to implement
  }

  // Group edges by component and take the highest confidence for each component
  const componentConfidenceMap = new Map<string, number>();

  for (const edge of implementsEdges) {
    const componentName = extractComponentNameFromEdge(edge.target);
    if (componentName) {
      const currentConfidence = componentConfidenceMap.get(componentName) || 0;
      componentConfidenceMap.set(
        componentName,
        Math.max(currentConfidence, edge.confidence)
      );
    }
  }

  // Calculate effective coverage with partial implementations
  let effectiveImplementations = 0;

  for (const confidence of componentConfidenceMap.values()) {
    if (confidence >= 0.8) {
      // Full implementation
      effectiveImplementations += 1.0;
    } else if (confidence >= 0.5) {
      // Partial implementation (errors but function exists)
      effectiveImplementations += 0.5;
    } else if (confidence >= 0.2) {
      // Stale but still counts as some implementation
      effectiveImplementations += 0.3;
    }
    // Very stale (confidence < 0.2) doesn't count
  }

  return effectiveImplementations / totalComponents;
}

/**
 * Calculate coverage metrics for a milestone
 * @param milestoneId - Milestone identifier
 * @param implementsEdges - Implements edges for the milestone
 * @param totalComponents - Total number of components in milestone
 * @returns Coverage metrics
 */
export function calculateCoverage(
  milestoneId: string,
  implementsEdges: KnowledgeGraphEdge[],
  totalComponents: number
): MilestoneCoverage {
  // Filter edges that belong to this milestone
  const milestoneEdges = implementsEdges.filter((edge) => {
    const componentName = extractComponentNameFromEdge(edge.target);
    return componentName && edge.target.includes(`${milestoneId}#`);
  });

  // Count unique implemented components (excluding very stale ones)
  const implementedComponents = new Set<string>();

  for (const edge of milestoneEdges) {
    if (edge.confidence >= 0.2) {
      // Exclude very stale annotations
      const componentName = extractComponentNameFromEdge(edge.target);
      if (componentName) {
        implementedComponents.add(componentName);
      }
    }
  }

  // Calculate effective coverage considering partial implementations
  const coverage = calculateEffectiveCoverage(milestoneEdges, totalComponents);

  // Calculate milestone confidence
  const milestoneConfidence = calculateMilestoneConfidence(milestoneEdges);

  return {
    milestoneId,
    totalComponents,
    implementedComponents: implementedComponents.size,
    coverage,
    confidence: milestoneConfidence,
    lastUpdated: new Date().toISOString(),
  };
}

/**
 * Determine exit code based on coverage metrics
 * @param coverageMetrics - Array of milestone coverage metrics
 * @param threshold - Coverage threshold (default: 0.5)
 * @returns Exit code (0=success, 60=coverage breach)
 */
export function determineExitCode(
  coverageMetrics: MilestoneCoverage[],
  threshold: number = 0.5
): number {
  const belowThreshold = coverageMetrics.filter((m) => m.coverage < threshold);

  if (belowThreshold.length > 0) {
    return 60; // Coverage breach
  }

  return 0; // Success
}
