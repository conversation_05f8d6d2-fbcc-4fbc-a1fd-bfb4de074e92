/**
 * @fileoverview Confidence scoring and coverage calculation
 * @implements milestone-M1.2#ConfidenceScoring
 */

import type {
  Annotation,
  MilestoneCoverage,
  KnowledgeGraphEdge,
} from './types.js';

/**
 * Calculate the number of days between two dates
 * @param dateString - ISO date string
 * @param now - Current date
 * @returns Number of days between dates
 */
function daysBetween(dateString: string, now: Date): number {
  const date = new Date(dateString);
  const diffTime = Math.abs(now.getTime() - date.getTime());
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
}

/**
 * Calculate confidence score for an annotation
 * @param annotation - Annotation to score
 * @param functionExists - Whether the function still exists
 * @returns Confidence score (0.0 to 1.0)
 */
export function calculateConfidence(
  annotation: Annotation,
  functionExists: boolean
): number {
  // If function doesn't exist, very low confidence (stale)
  if (!functionExists) {
    return 0.1;
  }

  // If annotation has errors, reduced confidence
  if (annotation.errors.some((e) => e.severity === 'error')) {
    return 0.5;
  }

  // Check how long since last verified
  const daysSinceVerified = daysBetween(annotation.lastVerified, new Date());

  // If not verified recently (>30 days), slightly reduced confidence
  if (daysSinceVerified > 30) {
    return 0.8;
  }

  // Valid annotation, function exists, recently verified
  return 1.0;
}

/**
 * Calculate milestone confidence from implements edges
 * @param implementsEdges - Array of implements edges for the milestone
 * @returns Average confidence score (0.0 to 1.0)
 */
export function calculateMilestoneConfidence(
  implementsEdges: KnowledgeGraphEdge[]
): number {
  if (implementsEdges.length === 0) {
    return 0.0;
  }

  const totalConfidence = implementsEdges.reduce(
    (sum, edge) => sum + edge.confidence,
    0
  );
  return totalConfidence / implementsEdges.length;
}

/**
 * Update confidence for stale edges
 * @param _edge - Edge to update (unused in current implementation)
 * @param staleReason - Reason for staleness
 * @returns Updated confidence score
 */
export function calculateStaleConfidence(
  _edge: KnowledgeGraphEdge,
  staleReason: string
): number {
  switch (staleReason) {
    case 'annotation_removed':
      // Annotation removed but function still exists
      return 0.2;
    case 'function_deleted':
      // Function no longer exists
      return 0.1;
    default:
      // Unknown stale reason, use conservative score
      return 0.1;
  }
}

/**
 * Calculate coverage metrics for a milestone
 * @param milestoneId - Milestone identifier
 * @param implementsEdges - Implements edges for the milestone
 * @param totalComponents - Total number of components in milestone
 * @returns Coverage metrics
 */
export function calculateCoverage(
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  _milestoneId: string,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  _implementsEdges: KnowledgeGraphEdge[],
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  _totalComponents: number
): MilestoneCoverage {
  // TODO: Implement in Task 09 - coverage-calculation
  throw new Error('calculateCoverage not yet implemented - Task 09');
}
