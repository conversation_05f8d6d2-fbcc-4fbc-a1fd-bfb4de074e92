/**
 * @fileoverview Component extraction from milestone specifications
 * @implements milestone-M1.2#ComponentExtraction
 */

import type { MilestoneComponent } from './types.js';

/**
 * Extract a section from milestone content by heading
 * @param content - Milestone content
 * @param sectionName - Section heading to find
 * @returns Section content or empty string if not found
 */
function extractSection(content: string, sectionName: string): string {
  const lines = content.split('\n');
  let inSection = false;
  let sectionLevel = 0;
  const sectionLines: string[] = [];

  for (const line of lines) {
    const headingMatch = line.match(/^(#{1,6})\s+(.+)$/);

    if (headingMatch && headingMatch[1] && headingMatch[2]) {
      const hashes = headingMatch[1];
      const title = headingMatch[2];
      const level = hashes.length;

      if (title.toLowerCase().includes(sectionName.toLowerCase())) {
        inSection = true;
        sectionLevel = level;
        continue;
      } else if (inSection && level <= sectionLevel) {
        // Found a heading at same or higher level, end of section
        break;
      }
    }

    if (inSection) {
      sectionLines.push(line);
    }
  }

  return sectionLines.join('\n');
}

/**
 * Extract component names from milestone specifications
 * @param milestoneContent - Milestone specification content
 * @param milestoneId - Milestone identifier
 * @returns Array of milestone components
 */
export function extractComponents(
  milestoneContent: string,
  milestoneId: string
): MilestoneComponent[] {
  const components: MilestoneComponent[] = [];

  // Strategy 1: Parse task breakdown section
  const taskSection = extractSection(milestoneContent, 'Task Breakdown');
  if (taskSection) {
    const taskMatches = taskSection.match(
      /### Task \d+: ([A-Za-z_][A-Za-z0-9_]*)/g
    );
    if (taskMatches) {
      for (const match of taskMatches) {
        const componentMatch = match.match(
          /### Task \d+: ([A-Za-z_][A-Za-z0-9_]*)/
        );
        if (componentMatch && componentMatch[1]) {
          const name = componentMatch[1];
          components.push({
            name,
            description: `Task component: ${name}`,
            milestoneId,
            required: true,
          });
        }
      }
    }
  }

  // Strategy 2: Parse deliverables section
  const deliverables = extractSection(milestoneContent, 'Deliverables');
  if (deliverables) {
    // Look for table rows that start with | and contain component names
    // Skip header rows and malformed tables
    const lines = deliverables.split('\n');

    for (const line of lines) {
      // Check if this looks like a valid table row (has proper pipe structure)
      const pipeCount = (line.match(/\|/g) || []).length;
      if (pipeCount < 2) continue; // Need at least 2 pipes for a valid table row

      const tableMatch = line.match(
        /^\|\s*([A-Za-z_][A-Za-z0-9_]*)\s*\|\s*([^|]+)\s*\|/
      );
      if (tableMatch && tableMatch[1] && tableMatch[2]) {
        const name = tableMatch[1];
        const description = tableMatch[2].trim();

        // Skip common header words and generic terms
        const skipWords = [
          'Component',
          'Description',
          'Artefact',
          'Path',
          'Content',
          'Name',
          'Purpose',
          'Invalid',
          'Missing',
          'NoMatch',
        ];
        if (!skipWords.includes(name) && description.length > 0) {
          // Avoid duplicates
          if (!components.some((c) => c.name === name)) {
            components.push({
              name,
              description: `Deliverable component: ${name}`,
              milestoneId,
              required: true,
            });
          }
        }
      }
    }
  }

  // Strategy 3: Parse directory layout for file-based components
  const directoryLayout = extractSection(milestoneContent, 'Directory Layout');
  if (directoryLayout) {
    // Look for TypeScript files in directory tree structure
    const lines = directoryLayout.split('\n');
    for (const line of lines) {
      // Match lines with tree structure and .ts files
      const fileMatch = line.match(/[├└]── ([A-Za-z_][A-Za-z0-9_-]*\.ts)$/);
      if (fileMatch && fileMatch[1]) {
        const fileName = fileMatch[1];
        const name = fileName.replace('.ts', '');
        // Convert to PascalCase for component name
        const componentName = name
          .split(/[-_]/)
          .map((part) => part.charAt(0).toUpperCase() + part.slice(1))
          .join('');

        // Avoid duplicates
        if (!components.some((c) => c.name === componentName)) {
          components.push({
            name: componentName,
            description: `File-based component: ${fileName}`,
            milestoneId,
            sourceFile: fileName,
            required: true,
          });
        }
      }
    }
  }

  // Filter to only required components as per specification
  return components.filter((c) => c.required);
}
