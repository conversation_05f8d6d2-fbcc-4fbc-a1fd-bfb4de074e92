/**
 * @fileoverview Main entry point for kg-sync-lib package
 * Provides bidirectional sync functionality for knowledge graphs
 */

// Export all types
export type {
  Annotation,
  ParseError,
  GraphUpdateResult,
  MilestoneCoverage,
  GraphError,
  MilestoneComponent,
  GitDiffResult,
  SyncOptions,
  SyncResult,
  KnowledgeGraphEdge,
  KnowledgeGraphNode,
} from './types.js';

// Export core functionality (to be implemented in subsequent tasks)
export { diffGit } from './diffGit.js';
export { parseAnnotations } from './parseAnnotations.js';
export { validateAnnotations, validateAnnotationFormat } from './validation.js';
export { updateGraph } from './updateGraph.js';
export { extractComponents } from './extractComponents.js';
export { calculateConfidence, calculateCoverage } from './confidence.js';

// Main sync function
export { syncKnowledgeGraph } from './sync.js';
