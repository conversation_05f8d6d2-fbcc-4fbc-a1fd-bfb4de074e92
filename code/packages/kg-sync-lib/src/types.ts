/**
 * Core types for bidirectional sync functionality
 */

export interface Annotation {
  milestoneId: string; // e.g., "M1.2"
  componentName: string; // e.g., "AnnotationParser"
  functionName: string; // e.g., "parseAnnotations"
  filePath: string; // e.g., "src/parser.ts"
  lineNumber: number; // Line where annotation appears
  confidence: number; // Always 1.0 for valid annotations
  lastVerified: string; // ISO timestamp
  errors: ParseError[]; // Validation errors if any
}

export interface ParseError {
  line: number;
  column: number;
  message: string;
  severity: 'error' | 'warning';
  suggestion?: string;
}

export interface GraphUpdateResult {
  nodesAdded: number;
  nodesUpdated: number;
  nodesMarkedStale: number;
  edgesAdded: number;
  edgesUpdated: number;
  edgesMarkedStale: number;
  coverageMetrics: MilestoneCoverage[];
  errors: GraphError[];
}

export interface MilestoneCoverage {
  milestoneId: string;
  totalComponents: number;
  implementedComponents: number;
  coverage: number; // 0.0 to 1.0
  confidence: number; // Average confidence of all edges
  lastUpdated: string; // ISO timestamp
}

export interface GraphError {
  type: 'parse' | 'validation' | 'update';
  message: string;
  filePath?: string;
  lineNumber?: number;
  severity: 'error' | 'warning';
}

export interface MilestoneComponent {
  name: string; // e.g., "AnnotationParser"
  description: string; // e.g., "Parses JSDoc annotations"
  milestoneId: string; // e.g., "M1.2"
  sourceFile?: string; // Optional: suggested implementation file
  required: boolean; // Whether component is required for milestone completion
}

export interface GitDiffResult {
  changedFiles: string[];
  addedFiles: string[];
  deletedFiles: string[];
  renamedFiles: Array<{ from: string; to: string }>;
  errors: string[];
}

export interface SyncOptions {
  since?: string; // Git reference to compare against
  dryRun?: boolean; // Don't write changes, just report
  outputDir?: string; // Directory for output files
  verbose?: boolean; // Detailed logging
}

export interface SyncResult {
  success: boolean;
  graphUpdateResult: GraphUpdateResult;
  diffResult: GitDiffResult;
  coverageMetrics: MilestoneCoverage[];
  exitCode: number; // 0=success, 60=coverage breach, 70=parse error, 1=error
  errors: GraphError[];
  warnings: GraphError[];
}

// Knowledge Graph types (extending existing types)
export interface KnowledgeGraphEdge {
  '@id': string;
  '@type': 'implements' | 'dependsOn' | 'contains';
  source: string;
  target: string;
  confidence: number;
  stale?: boolean;
  staleReason?: 'annotation_removed' | 'function_deleted' | 'file_moved';
  lastVerified: string;
}

export interface KnowledgeGraphNode {
  '@id': string;
  '@type': string;
  filePath?: string;
  functionName?: string;
  milestoneId?: string;
  componentName?: string;
  lastVerified?: string;
  [key: string]: unknown;
}
