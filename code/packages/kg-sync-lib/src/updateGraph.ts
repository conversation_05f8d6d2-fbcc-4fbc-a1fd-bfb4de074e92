/**
 * @fileoverview Incremental graph updates with confidence scoring
 * @implements milestone-M1.2#GraphUpdateCore
 */

import type {
  GraphUpdateResult,
  Annotation,
  KnowledgeGraphNode,
  KnowledgeGraphEdge,
  GraphError,
} from './types.js';

/**
 * Generate a unique ID for a node
 * @param type - Node type
 * @param identifier - Unique identifier
 * @returns Unique node ID
 */
function generateNodeId(type: string, identifier: string): string {
  return `${type}:${identifier}`;
}

/**
 * Generate a unique ID for an edge
 * @param type - Edge type
 * @param source - Source node ID
 * @param target - Target node ID
 * @returns Unique edge ID
 */
function generateEdgeId(type: string, source: string, target: string): string {
  return `${type}:${source}->${target}`;
}

/**
 * Create a function node from an annotation
 * @param annotation - Annotation to create node from
 * @returns Knowledge graph node
 */
function createFunctionNode(annotation: Annotation): KnowledgeGraphNode {
  const nodeId = generateNodeId(
    'function',
    `${annotation.filePath}#${annotation.functionName}`
  );

  return {
    '@id': nodeId,
    '@type': 'function',
    filePath: annotation.filePath,
    functionName: annotation.functionName,
    lastVerified: annotation.lastVerified,
  };
}

/**
 * Create a component node for a milestone component
 * @param milestoneId - Milestone identifier
 * @param componentName - Component name
 * @returns Knowledge graph node
 */
function createComponentNode(
  milestoneId: string,
  componentName: string
): KnowledgeGraphNode {
  const nodeId = generateNodeId('component', `${milestoneId}#${componentName}`);

  return {
    '@id': nodeId,
    '@type': 'component',
    milestoneId,
    componentName,
    lastVerified: new Date().toISOString(),
  };
}

/**
 * Create an implements edge from function to component
 * @param functionNode - Function node
 * @param componentNode - Component node
 * @param confidence - Confidence score
 * @returns Knowledge graph edge
 */
function createImplementsEdge(
  functionNode: KnowledgeGraphNode,
  componentNode: KnowledgeGraphNode,
  confidence: number
): KnowledgeGraphEdge {
  const edgeId = generateEdgeId(
    'implements',
    functionNode['@id'],
    componentNode['@id']
  );

  return {
    '@id': edgeId,
    '@type': 'implements',
    source: functionNode['@id'],
    target: componentNode['@id'],
    confidence,
    lastVerified: new Date().toISOString(),
  };
}

/**
 * Update knowledge graph with new annotations
 * @param currentGraph - Current knowledge graph nodes and edges
 * @param newAnnotations - New annotations to process
 * @param changedFiles - List of changed files
 * @returns Graph update result
 */
export function updateGraph(
  currentGraph: { nodes: KnowledgeGraphNode[]; edges: KnowledgeGraphEdge[] },
  newAnnotations: Annotation[],
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  _changedFiles: string[]
): GraphUpdateResult {
  const result: GraphUpdateResult = {
    nodesAdded: 0,
    nodesUpdated: 0,
    nodesMarkedStale: 0,
    edgesAdded: 0,
    edgesUpdated: 0,
    edgesMarkedStale: 0,
    coverageMetrics: [],
    errors: [],
  };

  const errors: GraphError[] = [];
  const updatedNodes = new Map<string, KnowledgeGraphNode>();
  const updatedEdges = new Map<string, KnowledgeGraphEdge>();

  // Initialize with existing nodes and edges
  for (const node of currentGraph.nodes) {
    updatedNodes.set(node['@id'], { ...node });
  }
  for (const edge of currentGraph.edges) {
    updatedEdges.set(edge['@id'], { ...edge });
  }

  // Process new annotations
  for (const annotation of newAnnotations) {
    try {
      // Create or update function node
      const functionNode = createFunctionNode(annotation);
      const existingFunctionNode = updatedNodes.get(functionNode['@id']);

      if (existingFunctionNode) {
        // Update existing node
        existingFunctionNode.lastVerified = functionNode.lastVerified;
        result.nodesUpdated++;
      } else {
        // Add new node
        updatedNodes.set(functionNode['@id'], functionNode);
        result.nodesAdded++;
      }

      // Create or update component node
      const componentNode = createComponentNode(
        annotation.milestoneId,
        annotation.componentName
      );
      const existingComponentNode = updatedNodes.get(componentNode['@id']);

      if (!existingComponentNode) {
        updatedNodes.set(componentNode['@id'], componentNode);
        result.nodesAdded++;
      }

      // Create or update implements edge
      const implementsEdge = createImplementsEdge(
        functionNode,
        componentNode,
        annotation.confidence
      );
      const existingEdge = updatedEdges.get(implementsEdge['@id']);

      if (existingEdge) {
        // Update existing edge
        existingEdge.confidence = implementsEdge.confidence;
        existingEdge.lastVerified = implementsEdge.lastVerified;
        existingEdge.stale = false; // Reset stale flag since annotation exists
        delete existingEdge.staleReason;
        result.edgesUpdated++;
      } else {
        // Add new edge
        updatedEdges.set(implementsEdge['@id'], implementsEdge);
        result.edgesAdded++;
      }
    } catch (error) {
      errors.push({
        type: 'update',
        message: `Failed to process annotation for ${annotation.functionName}: ${error instanceof Error ? error.message : 'Unknown error'}`,
        filePath: annotation.filePath,
        lineNumber: annotation.lineNumber,
        severity: 'error',
      });
    }
  }

  // Mark stale edges for functions that no longer have annotations
  // This is a simplified implementation - in a full implementation, we would
  // need to track which files were changed and which annotations were removed
  for (const [, edge] of updatedEdges) {
    if (edge['@type'] === 'implements') {
      // Check if this edge still has a corresponding annotation
      const hasCorrespondingAnnotation = newAnnotations.some((annotation) => {
        const functionNodeId = generateNodeId(
          'function',
          `${annotation.filePath}#${annotation.functionName}`
        );
        const componentNodeId = generateNodeId(
          'component',
          `${annotation.milestoneId}#${annotation.componentName}`
        );
        return (
          edge.source === functionNodeId && edge.target === componentNodeId
        );
      });

      if (!hasCorrespondingAnnotation && !edge.stale) {
        // Mark as stale if no corresponding annotation found
        edge.stale = true;
        edge.staleReason = 'annotation_removed';
        edge.confidence = 0.2; // Reduce confidence for stale edges
        edge.lastVerified = new Date().toISOString();
        result.edgesMarkedStale++;
      }
    }
  }

  // Update the result with final node and edge arrays
  result.errors = errors;
  return result;
}
