/**
 * @fileoverview Enhanced validation rules for annotations
 * @implements milestone-M1.2#ValidationEngine
 */

import type { Annotation, ParseError } from './types.js';

// Enhanced validation patterns
const MILESTONE_ID_PATTERN = /^M\d+(?:\.\d+)*$/;
const COMPONENT_NAME_PATTERN = /^[A-Za-z_][A-Za-z0-9_]*$/;
const RESERVED_COMPONENT_NAMES = new Set([
  'constructor',
  'prototype',
  'toString',
  'valueOf',
  'hasOwnProperty',
]);

/**
 * Validate a collection of annotations for advanced rules
 * @param annotations - Array of annotations to validate
 * @returns Enhanced annotations with additional validation errors
 */
export function validateAnnotations(annotations: Annotation[]): Annotation[] {
  const validatedAnnotations = annotations.map((annotation) => ({
    ...annotation,
    errors: [...annotation.errors],
  }));

  // Check for duplicate annotations
  checkDuplicateAnnotations(validatedAnnotations);

  // Validate milestone ID existence (basic check)
  validateMilestoneIds(validatedAnnotations);

  // Check for reserved component names
  validateComponentNames(validatedAnnotations);

  // Validate annotation context
  validateAnnotationContext(validatedAnnotations);

  // Recalculate confidence scores based on new errors
  recalculateConfidence(validatedAnnotations);

  return validatedAnnotations;
}

/**
 * Check for duplicate @implements annotations for the same component
 */
function checkDuplicateAnnotations(annotations: Annotation[]): void {
  const componentMap = new Map<string, Annotation[]>();

  // Group annotations by component identifier
  for (const annotation of annotations) {
    if (annotation.milestoneId && annotation.componentName) {
      const key = `${annotation.milestoneId}#${annotation.componentName}`;
      if (!componentMap.has(key)) {
        componentMap.set(key, []);
      }
      componentMap.get(key)!.push(annotation);
    }
  }

  // Check for duplicates
  for (const [componentKey, annotationGroup] of componentMap) {
    if (annotationGroup.length > 1) {
      // Mark all but the first as duplicates
      for (let i = 1; i < annotationGroup.length; i++) {
        const annotation = annotationGroup[i];
        if (annotation) {
          annotation.errors.push({
            line: annotation.lineNumber,
            column: 1,
            message: `Duplicate @implements annotation for ${componentKey}`,
            severity: 'warning',
            suggestion: `Remove duplicate annotation or use different component name`,
          });
        }
      }

      // Add warning to the first one too
      const firstAnnotation = annotationGroup[0];
      if (firstAnnotation) {
        firstAnnotation.errors.push({
          line: firstAnnotation.lineNumber,
          column: 1,
          message: `Multiple @implements annotations found for ${componentKey}`,
          severity: 'warning',
          suggestion: `Consider consolidating or using different component names`,
        });
      }
    }
  }
}

/**
 * Validate milestone ID format and existence
 */
function validateMilestoneIds(annotations: Annotation[]): void {
  for (const annotation of annotations) {
    if (annotation.milestoneId) {
      // Check format
      if (!MILESTONE_ID_PATTERN.test(annotation.milestoneId)) {
        annotation.errors.push({
          line: annotation.lineNumber,
          column: 1,
          message: `Invalid milestone ID format: "${annotation.milestoneId}"`,
          severity: 'error',
          suggestion: 'Use format: M1, M1.2, M1.2.3, etc.',
        });
      }

      // Check for reasonable milestone numbers
      const parts = annotation.milestoneId.substring(1).split('.');
      for (const part of parts) {
        const num = parseInt(part, 10);
        if (num > 999) {
          annotation.errors.push({
            line: annotation.lineNumber,
            column: 1,
            message: `Milestone number too large: ${num}`,
            severity: 'warning',
            suggestion:
              'Consider using smaller milestone numbers for better organization',
          });
        }
      }
    }
  }
}

/**
 * Validate component names for reserved words and conventions
 */
function validateComponentNames(annotations: Annotation[]): void {
  for (const annotation of annotations) {
    if (annotation.componentName) {
      // Check format
      if (!COMPONENT_NAME_PATTERN.test(annotation.componentName)) {
        annotation.errors.push({
          line: annotation.lineNumber,
          column: 1,
          message: `Invalid component name format: "${annotation.componentName}"`,
          severity: 'error',
          suggestion:
            'Use alphanumeric characters and underscores only, starting with letter or underscore',
        });
      }

      // Check for reserved names
      if (
        RESERVED_COMPONENT_NAMES.has(annotation.componentName.toLowerCase())
      ) {
        annotation.errors.push({
          line: annotation.lineNumber,
          column: 1,
          message: `Reserved component name: "${annotation.componentName}"`,
          severity: 'warning',
          suggestion: 'Use a different component name to avoid conflicts',
        });
      }

      // Check naming conventions
      if (annotation.componentName.length < 3) {
        annotation.errors.push({
          line: annotation.lineNumber,
          column: 1,
          message: `Component name too short: "${annotation.componentName}"`,
          severity: 'warning',
          suggestion:
            'Use descriptive component names with at least 3 characters',
        });
      }

      if (annotation.componentName.length > 50) {
        annotation.errors.push({
          line: annotation.lineNumber,
          column: 1,
          message: `Component name too long: "${annotation.componentName}"`,
          severity: 'warning',
          suggestion:
            'Use shorter, more concise component names (max 50 characters)',
        });
      }

      // Check for PascalCase convention
      if (!/^[A-Z][A-Za-z0-9_]*$/.test(annotation.componentName)) {
        annotation.errors.push({
          line: annotation.lineNumber,
          column: 1,
          message: `Component name should use PascalCase: "${annotation.componentName}"`,
          severity: 'warning',
          suggestion:
            'Use PascalCase for component names (e.g., AnnotationParser)',
        });
      }
    }
  }
}

/**
 * Validate annotation context and placement
 */
function validateAnnotationContext(annotations: Annotation[]): void {
  for (const annotation of annotations) {
    // Check if function name matches component name pattern
    if (annotation.functionName && annotation.componentName) {
      const functionLower = annotation.functionName.toLowerCase();
      const componentLower = annotation.componentName.toLowerCase();

      // Warn if function and component names are very different
      if (
        !functionLower.includes(componentLower.substring(0, 5)) &&
        !componentLower.includes(functionLower.substring(0, 5))
      ) {
        annotation.errors.push({
          line: annotation.lineNumber,
          column: 1,
          message: `Function name "${annotation.functionName}" doesn't match component "${annotation.componentName}"`,
          severity: 'warning',
          suggestion:
            'Consider using similar names for function and component for clarity',
        });
      }
    }

    // Check file path conventions
    if (annotation.filePath) {
      const fileName = annotation.filePath.split('/').pop() || '';
      const baseName = fileName.replace(/\.(ts|tsx|js|jsx)$/, '');

      if (
        annotation.componentName &&
        baseName.toLowerCase() !== annotation.componentName.toLowerCase()
      ) {
        annotation.errors.push({
          line: annotation.lineNumber,
          column: 1,
          message: `File name "${fileName}" doesn't match component "${annotation.componentName}"`,
          severity: 'warning',
          suggestion: 'Consider naming files to match their primary components',
        });
      }
    }
  }
}

/**
 * Recalculate confidence scores based on validation errors
 */
function recalculateConfidence(annotations: Annotation[]): void {
  for (const annotation of annotations) {
    const errorCount = annotation.errors.filter(
      (e) => e.severity === 'error'
    ).length;
    const warningCount = annotation.errors.filter(
      (e) => e.severity === 'warning'
    ).length;

    if (errorCount > 0) {
      // Errors significantly reduce confidence
      annotation.confidence = Math.max(0.1, 1.0 - errorCount * 0.3);
    } else if (warningCount > 0) {
      // Warnings slightly reduce confidence
      annotation.confidence = Math.max(0.7, 1.0 - warningCount * 0.1);
    } else {
      // No issues, full confidence
      annotation.confidence = 1.0;
    }
  }
}

/**
 * Validate annotation format with detailed error reporting
 * @param annotationValue - The raw annotation value
 * @param lineNumber - Line number for error reporting
 * @returns Array of validation errors
 */
export function validateAnnotationFormat(
  annotationValue: string,
  lineNumber: number
): ParseError[] {
  const errors: ParseError[] = [];

  if (!annotationValue.trim()) {
    errors.push({
      line: lineNumber,
      column: 1,
      message: '@implements tag is empty',
      severity: 'error',
      suggestion:
        'Add milestone ID and component name: @implements milestone-M1.2#ComponentName',
    });
    return errors;
  }

  const trimmed = annotationValue.trim();

  // Check for milestone- prefix
  if (!trimmed.startsWith('milestone-')) {
    errors.push({
      line: lineNumber,
      column: 1,
      message: 'Missing "milestone-" prefix',
      severity: 'error',
      suggestion: 'Use format: @implements milestone-M1.2#ComponentName',
    });
  }

  // Check for # separator
  if (!trimmed.includes('#')) {
    errors.push({
      line: lineNumber,
      column: 1,
      message: 'Missing "#" separator between milestone ID and component name',
      severity: 'error',
      suggestion: 'Use format: @implements milestone-M1.2#ComponentName',
    });
  }

  // Check for multiple # characters
  const hashCount = (trimmed.match(/#/g) || []).length;
  if (hashCount > 1) {
    errors.push({
      line: lineNumber,
      column: 1,
      message: 'Multiple "#" characters found',
      severity: 'error',
      suggestion:
        'Use only one "#" to separate milestone ID and component name',
    });
  }

  return errors;
}
