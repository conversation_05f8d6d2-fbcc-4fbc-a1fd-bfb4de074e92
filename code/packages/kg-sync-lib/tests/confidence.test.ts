/**
 * @fileoverview Tests for confidence scoring functionality
 */

import {
  calculateConfidence,
  calculateMilestoneConfidence,
  calculateStaleConfidence,
} from '../src/confidence.js';
import type { Annotation, KnowledgeGraphEdge } from '../src/types.js';

describe('confidence scoring', () => {
  const baseAnnotation: Annotation = {
    milestoneId: 'M1.2',
    componentName: 'TestComponent',
    functionName: 'testFunction',
    filePath: 'src/test.ts',
    lineNumber: 10,
    confidence: 1.0,
    lastVerified: new Date().toISOString(),
    errors: [],
  };

  describe('calculateConfidence', () => {
    it('should return 1.0 for valid annotation with existing function', () => {
      const result = calculateConfidence(baseAnnotation, true);
      expect(result).toBe(1.0);
    });

    it('should return 0.1 when function does not exist', () => {
      const result = calculateConfidence(baseAnnotation, false);
      expect(result).toBe(0.1);
    });

    it('should return 0.5 when annotation has errors', () => {
      const annotationWithErrors: Annotation = {
        ...baseAnnotation,
        errors: [
          {
            line: 10,
            column: 5,
            message: 'Invalid milestone ID format',
            severity: 'error',
          },
        ],
      };

      const result = calculateConfidence(annotationWithErrors, true);
      expect(result).toBe(0.5);
    });

    it('should return 1.0 when annotation has only warnings', () => {
      const annotationWithWarnings: Annotation = {
        ...baseAnnotation,
        errors: [
          {
            line: 10,
            column: 5,
            message: 'Component name mismatch with file name',
            severity: 'warning',
          },
        ],
      };

      const result = calculateConfidence(annotationWithWarnings, true);
      expect(result).toBe(1.0);
    });

    it('should return 0.8 for old annotations (>30 days)', () => {
      const oldDate = new Date();
      oldDate.setDate(oldDate.getDate() - 35); // 35 days ago

      const oldAnnotation: Annotation = {
        ...baseAnnotation,
        lastVerified: oldDate.toISOString(),
      };

      const result = calculateConfidence(oldAnnotation, true);
      expect(result).toBe(0.8);
    });

    it('should return 1.0 for recent annotations (≤30 days)', () => {
      const recentDate = new Date();
      recentDate.setDate(recentDate.getDate() - 25); // 25 days ago

      const recentAnnotation: Annotation = {
        ...baseAnnotation,
        lastVerified: recentDate.toISOString(),
      };

      const result = calculateConfidence(recentAnnotation, true);
      expect(result).toBe(1.0);
    });

    it('should prioritize function existence over other factors', () => {
      const oldAnnotationWithErrors: Annotation = {
        ...baseAnnotation,
        lastVerified: new Date(
          Date.now() - 40 * 24 * 60 * 60 * 1000
        ).toISOString(), // 40 days ago
        errors: [
          {
            line: 10,
            column: 5,
            message: 'Some error',
            severity: 'error',
          },
        ],
      };

      // Function doesn't exist - should return 0.1 regardless of other issues
      const result = calculateConfidence(oldAnnotationWithErrors, false);
      expect(result).toBe(0.1);
    });

    it('should prioritize errors over age', () => {
      const recentAnnotationWithErrors: Annotation = {
        ...baseAnnotation,
        lastVerified: new Date().toISOString(), // Recent
        errors: [
          {
            line: 10,
            column: 5,
            message: 'Some error',
            severity: 'error',
          },
        ],
      };

      // Has errors - should return 0.5 even if recent
      const result = calculateConfidence(recentAnnotationWithErrors, true);
      expect(result).toBe(0.5);
    });
  });

  describe('calculateMilestoneConfidence', () => {
    const createEdge = (confidence: number): KnowledgeGraphEdge => ({
      '@id': `edge-${confidence}`,
      '@type': 'implements',
      source: 'function:test',
      target: 'component:test',
      confidence,
      lastVerified: new Date().toISOString(),
    });

    it('should return 0.0 for empty edges array', () => {
      const result = calculateMilestoneConfidence([]);
      expect(result).toBe(0.0);
    });

    it('should return average confidence for single edge', () => {
      const edges = [createEdge(0.8)];
      const result = calculateMilestoneConfidence(edges);
      expect(result).toBe(0.8);
    });

    it('should calculate average confidence for multiple edges', () => {
      const edges = [
        createEdge(1.0),
        createEdge(0.8),
        createEdge(0.5),
        createEdge(0.2),
      ];

      const result = calculateMilestoneConfidence(edges);
      const expected = (1.0 + 0.8 + 0.5 + 0.2) / 4; // 0.625
      expect(result).toBe(expected);
    });

    it('should handle all high confidence edges', () => {
      const edges = [createEdge(1.0), createEdge(1.0), createEdge(1.0)];

      const result = calculateMilestoneConfidence(edges);
      expect(result).toBe(1.0);
    });

    it('should handle all low confidence edges', () => {
      const edges = [createEdge(0.1), createEdge(0.1), createEdge(0.2)];

      const result = calculateMilestoneConfidence(edges);
      expect(result).toBeCloseTo(0.133, 3);
    });
  });

  describe('calculateStaleConfidence', () => {
    const mockEdge: KnowledgeGraphEdge = {
      '@id': 'test-edge',
      '@type': 'implements',
      source: 'function:test',
      target: 'component:test',
      confidence: 1.0,
      lastVerified: new Date().toISOString(),
    };

    it('should return 0.2 for annotation_removed', () => {
      const result = calculateStaleConfidence(mockEdge, 'annotation_removed');
      expect(result).toBe(0.2);
    });

    it('should return 0.1 for function_deleted', () => {
      const result = calculateStaleConfidence(mockEdge, 'function_deleted');
      expect(result).toBe(0.1);
    });

    it('should return 0.1 for unknown stale reason', () => {
      const result = calculateStaleConfidence(mockEdge, 'unknown_reason');
      expect(result).toBe(0.1);
    });

    it('should return 0.1 for empty stale reason', () => {
      const result = calculateStaleConfidence(mockEdge, '');
      expect(result).toBe(0.1);
    });

    it('should handle case sensitivity', () => {
      const result1 = calculateStaleConfidence(mockEdge, 'ANNOTATION_REMOVED');
      const result2 = calculateStaleConfidence(mockEdge, 'Annotation_Removed');

      // Should treat as unknown and return 0.1
      expect(result1).toBe(0.1);
      expect(result2).toBe(0.1);
    });
  });

  describe('edge cases', () => {
    it('should handle invalid date strings gracefully', () => {
      const invalidDateAnnotation: Annotation = {
        ...baseAnnotation,
        lastVerified: 'invalid-date',
      };

      // Should not throw error, might return NaN days but still work
      const result = calculateConfidence(invalidDateAnnotation, true);
      expect(typeof result).toBe('number');
      expect(result).toBeGreaterThanOrEqual(0);
      expect(result).toBeLessThanOrEqual(1);
    });

    it('should handle future dates', () => {
      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + 10); // 10 days in future

      const futureAnnotation: Annotation = {
        ...baseAnnotation,
        lastVerified: futureDate.toISOString(),
      };

      const result = calculateConfidence(futureAnnotation, true);
      expect(result).toBe(1.0); // Should treat as recent
    });

    it('should handle mixed error severities', () => {
      const mixedErrorsAnnotation: Annotation = {
        ...baseAnnotation,
        errors: [
          {
            line: 10,
            column: 5,
            message: 'Warning message',
            severity: 'warning',
          },
          {
            line: 11,
            column: 3,
            message: 'Error message',
            severity: 'error',
          },
        ],
      };

      // Should return 0.5 because there's at least one error
      const result = calculateConfidence(mixedErrorsAnnotation, true);
      expect(result).toBe(0.5);
    });
  });
});
