/**
 * @fileoverview Tests for coverage calculation functionality
 */

import { calculateCoverage, determineExitCode } from '../src/confidence.js';
import type { KnowledgeGraphEdge, MilestoneCoverage } from '../src/types.js';

describe('coverage calculation', () => {
  const createEdge = (
    milestoneId: string,
    componentName: string,
    confidence: number
  ): KnowledgeGraphEdge => ({
    '@id': `implements:function:src/test.ts#testFunction->component:${milestoneId}#${componentName}`,
    '@type': 'implements',
    source: 'function:src/test.ts#testFunction',
    target: `component:${milestoneId}#${componentName}`,
    confidence,
    lastVerified: new Date().toISOString(),
  });

  describe('calculateCoverage', () => {
    it('should calculate basic coverage correctly', () => {
      const edges = [
        createEdge('M1.2', 'ComponentA', 1.0),
        createEdge('M1.2', 'ComponentB', 0.8),
      ];

      const result = calculateCoverage('M1.2', edges, 3);

      expect(result.milestoneId).toBe('M1.2');
      expect(result.totalComponents).toBe(3);
      expect(result.implementedComponents).toBe(2);
      expect(result.coverage).toBeCloseTo(0.67, 2); // (1.0 + 1.0) / 3
      expect(result.confidence).toBe(0.9); // (1.0 + 0.8) / 2
      expect(result.lastUpdated).toBeDefined();
    });

    it('should handle partial implementations correctly', () => {
      const edges = [
        createEdge('M1.2', 'ComponentA', 1.0), // Full implementation
        createEdge('M1.2', 'ComponentB', 0.5), // Partial implementation (errors)
        createEdge('M1.2', 'ComponentC', 0.2), // Stale implementation
      ];

      const result = calculateCoverage('M1.2', edges, 4);

      expect(result.totalComponents).toBe(4);
      expect(result.implementedComponents).toBe(3);
      // Effective coverage: (1.0 + 0.5 + 0.3) / 4 = 0.45
      expect(result.coverage).toBeCloseTo(0.45, 2);
    });

    it('should exclude very stale annotations', () => {
      const edges = [
        createEdge('M1.2', 'ComponentA', 1.0), // Full implementation
        createEdge('M1.2', 'ComponentB', 0.1), // Very stale - should be excluded
      ];

      const result = calculateCoverage('M1.2', edges, 2);

      expect(result.implementedComponents).toBe(1); // Only ComponentA counted
      expect(result.coverage).toBe(0.5); // 1.0 / 2
    });

    it('should filter edges by milestone ID', () => {
      const edges = [
        createEdge('M1.2', 'ComponentA', 1.0),
        createEdge('M1.3', 'ComponentB', 1.0), // Different milestone
        createEdge('M1.2', 'ComponentC', 0.8),
      ];

      const result = calculateCoverage('M1.2', edges, 2);

      expect(result.implementedComponents).toBe(2); // Only M1.2 components
      expect(result.coverage).toBe(1.0); // (1.0 + 1.0) / 2
    });

    it('should handle multiple edges for same component', () => {
      const edges = [
        createEdge('M1.2', 'ComponentA', 0.5), // Lower confidence
        createEdge('M1.2', 'ComponentA', 1.0), // Higher confidence - should be used
        createEdge('M1.2', 'ComponentB', 0.8),
      ];

      const result = calculateCoverage('M1.2', edges, 2);

      expect(result.implementedComponents).toBe(2);
      expect(result.coverage).toBe(1.0); // (1.0 + 1.0) / 2 - uses highest confidence per component
    });

    it('should handle zero components', () => {
      const edges: KnowledgeGraphEdge[] = [];

      const result = calculateCoverage('M1.2', edges, 0);

      expect(result.totalComponents).toBe(0);
      expect(result.implementedComponents).toBe(0);
      expect(result.coverage).toBe(1.0); // 100% coverage when no components to implement
      expect(result.confidence).toBe(0.0); // No edges means 0 confidence
    });

    it('should handle no implementations', () => {
      const edges: KnowledgeGraphEdge[] = [];

      const result = calculateCoverage('M1.2', edges, 3);

      expect(result.totalComponents).toBe(3);
      expect(result.implementedComponents).toBe(0);
      expect(result.coverage).toBe(0.0);
      expect(result.confidence).toBe(0.0);
    });

    it('should handle all stale implementations', () => {
      const edges = [
        createEdge('M1.2', 'ComponentA', 0.1), // Very stale
        createEdge('M1.2', 'ComponentB', 0.05), // Very stale
      ];

      const result = calculateCoverage('M1.2', edges, 2);

      expect(result.implementedComponents).toBe(0); // All excluded due to low confidence
      expect(result.coverage).toBe(0.0);
    });

    it('should calculate confidence correctly', () => {
      const edges = [
        createEdge('M1.2', 'ComponentA', 1.0),
        createEdge('M1.2', 'ComponentB', 0.8),
        createEdge('M1.2', 'ComponentC', 0.5),
      ];

      const result = calculateCoverage('M1.2', edges, 3);

      const expectedConfidence = (1.0 + 0.8 + 0.5) / 3;
      expect(result.confidence).toBeCloseTo(expectedConfidence, 3);
    });

    it('should handle malformed edge targets gracefully', () => {
      const malformedEdge: KnowledgeGraphEdge = {
        '@id': 'test-edge',
        '@type': 'implements',
        source: 'function:test',
        target: 'invalid-target-format', // Malformed target
        confidence: 1.0,
        lastVerified: new Date().toISOString(),
      };

      const validEdge = createEdge('M1.2', 'ComponentA', 1.0);
      const edges = [malformedEdge, validEdge];

      const result = calculateCoverage('M1.2', edges, 2);

      expect(result.implementedComponents).toBe(1); // Only valid edge counted
      expect(result.coverage).toBe(0.5); // 1.0 / 2
    });
  });

  describe('determineExitCode', () => {
    const createCoverageMetric = (
      milestoneId: string,
      coverage: number
    ): MilestoneCoverage => ({
      milestoneId,
      totalComponents: 10,
      implementedComponents: Math.floor(coverage * 10),
      coverage,
      confidence: 0.8,
      lastUpdated: new Date().toISOString(),
    });

    it('should return 0 for successful coverage', () => {
      const metrics = [
        createCoverageMetric('M1.1', 0.8),
        createCoverageMetric('M1.2', 0.6),
        createCoverageMetric('M1.3', 1.0),
      ];

      const exitCode = determineExitCode(metrics);
      expect(exitCode).toBe(0);
    });

    it('should return 60 for coverage breach', () => {
      const metrics = [
        createCoverageMetric('M1.1', 0.8),
        createCoverageMetric('M1.2', 0.3), // Below 0.5 threshold
        createCoverageMetric('M1.3', 1.0),
      ];

      const exitCode = determineExitCode(metrics);
      expect(exitCode).toBe(60);
    });

    it('should use custom threshold', () => {
      const metrics = [
        createCoverageMetric('M1.1', 0.6),
        createCoverageMetric('M1.2', 0.7),
      ];

      // With default threshold (0.5), should pass
      expect(determineExitCode(metrics)).toBe(0);

      // With custom threshold (0.8), should fail
      expect(determineExitCode(metrics, 0.8)).toBe(60);
    });

    it('should handle empty metrics array', () => {
      const exitCode = determineExitCode([]);
      expect(exitCode).toBe(0); // No milestones means success
    });

    it('should handle multiple breaches', () => {
      const metrics = [
        createCoverageMetric('M1.1', 0.2), // Below threshold
        createCoverageMetric('M1.2', 0.3), // Below threshold
        createCoverageMetric('M1.3', 0.8), // Above threshold
      ];

      const exitCode = determineExitCode(metrics);
      expect(exitCode).toBe(60); // Still returns 60 even with multiple breaches
    });

    it('should handle edge case at threshold', () => {
      const metrics = [
        createCoverageMetric('M1.1', 0.5), // Exactly at threshold
        createCoverageMetric('M1.2', 0.49), // Just below threshold
      ];

      const exitCode = determineExitCode(metrics);
      expect(exitCode).toBe(60); // 0.49 is below 0.5
    });
  });

  describe('edge cases', () => {
    it('should handle very large numbers of components', () => {
      const edges = Array.from({ length: 1000 }, (_, i) =>
        createEdge('M1.2', `Component${i}`, 1.0)
      );

      const result = calculateCoverage('M1.2', edges, 1000);

      expect(result.implementedComponents).toBe(1000);
      expect(result.coverage).toBe(1.0);
      expect(result.confidence).toBe(1.0);
    });

    it('should handle mixed confidence levels correctly', () => {
      const edges = [
        createEdge('M1.2', 'HighConf', 1.0), // Full: 1.0
        createEdge('M1.2', 'MedConf', 0.8), // Full: 1.0
        createEdge('M1.2', 'LowConf', 0.5), // Partial: 0.5
        createEdge('M1.2', 'StaleConf', 0.2), // Stale: 0.3
        createEdge('M1.2', 'VeryStale', 0.1), // Excluded: 0.0
      ];

      const result = calculateCoverage('M1.2', edges, 5);

      // Expected: (1.0 + 1.0 + 0.5 + 0.3 + 0.0) / 5 = 0.56
      expect(result.coverage).toBeCloseTo(0.56, 2);
      expect(result.implementedComponents).toBe(4); // VeryStale excluded from count
    });
  });
});
