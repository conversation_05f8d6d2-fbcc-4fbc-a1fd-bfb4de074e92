/**
 * @fileoverview Tests for git diff functionality
 */

import { diffGit } from '../src/diffGit.js';
import type { SyncOptions } from '../src/types.js';

// Mock simple-git
jest.mock('simple-git', () => ({
  simpleGit: jest.fn(() => ({
    diffSummary: jest.fn(),
    status: jest.fn(),
  })),
}));

import { simpleGit } from 'simple-git';

const mockGit = {
  diffSummary: jest.fn(),
  status: jest.fn(),
  checkIsRepo: jest.fn(),
  revparse: jest.fn(),
};

(simpleGit as jest.Mock).mockReturnValue(mockGit);

describe('diffGit', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Default mocks for successful operations
    mockGit.checkIsRepo.mockResolvedValue(true);
    mockGit.revparse.mockResolvedValue('abc123');
    mockGit.status.mockResolvedValue({
      conflicted: [],
      renamed: [],
      not_added: [],
    });
    mockGit.diffSummary.mockResolvedValue({
      files: [],
    });
  });

  it('should detect changed files', async () => {
    // Mock git diff response
    mockGit.diffSummary.mockResolvedValue({
      files: [
        {
          file: 'src/test.ts',
          insertions: 5,
          deletions: 2,
          binary: false,
        },
        {
          file: 'src/new.ts',
          insertions: 10,
          deletions: 0,
          binary: false,
        },
        {
          file: 'src/deleted.ts',
          insertions: 0,
          deletions: 8,
          binary: false,
        },
      ],
    });

    mockGit.status.mockResolvedValue({
      conflicted: [],
      renamed: [],
      not_added: [],
    });

    const options: SyncOptions = {
      since: 'HEAD~1',
      verbose: false,
    };

    const result = await diffGit(options);

    expect(result.changedFiles).toEqual(['src/test.ts']);
    expect(result.addedFiles).toEqual(['src/new.ts']);
    expect(result.deletedFiles).toEqual(['src/deleted.ts']);
    expect(result.renamedFiles).toEqual([]);
    expect(result.errors).toEqual([]);
  });

  it('should detect renamed files', async () => {
    mockGit.diffSummary.mockResolvedValue({
      files: [],
    });

    mockGit.status.mockResolvedValue({
      conflicted: [],
      renamed: [
        {
          from: 'src/old.ts',
          to: 'src/new.ts',
        },
      ],
      not_added: [],
    });

    const options: SyncOptions = {
      since: 'origin/main',
    };

    const result = await diffGit(options);

    expect(result.renamedFiles).toEqual([
      {
        from: 'src/old.ts',
        to: 'src/new.ts',
      },
    ]);
  });

  it('should filter out binary and non-source files', async () => {
    mockGit.diffSummary.mockResolvedValue({
      files: [
        {
          file: 'src/test.ts',
          insertions: 5,
          deletions: 0,
          binary: false,
        },
        {
          file: 'image.png',
          insertions: 0,
          deletions: 0,
          binary: true,
        },
        {
          file: 'config.json',
          insertions: 2,
          deletions: 0,
          binary: false,
        },
      ],
    });

    mockGit.status.mockResolvedValue({
      conflicted: [],
      renamed: [],
      not_added: [],
    });

    const options: SyncOptions = {};
    const result = await diffGit(options);

    expect(result.addedFiles).toEqual(['src/test.ts']);
    expect(result.addedFiles).not.toContain('image.png');
    expect(result.addedFiles).not.toContain('config.json');
  });

  it('should handle git errors gracefully', async () => {
    mockGit.diffSummary.mockRejectedValue(new Error('Git command failed'));

    const options: SyncOptions = {
      since: 'invalid-ref',
    };

    const result = await diffGit(options);

    expect(result.errors).toEqual(['Git diff failed: Git command failed']);
    expect(result.changedFiles).toEqual([]);
    expect(result.addedFiles).toEqual([]);
    expect(result.deletedFiles).toEqual([]);
  });

  it('should use default since value when not provided', async () => {
    mockGit.diffSummary.mockResolvedValue({
      files: [],
    });

    mockGit.status.mockResolvedValue({
      conflicted: [],
      renamed: [],
      not_added: [],
    });

    const options: SyncOptions = {};
    await diffGit(options);

    expect(mockGit.diffSummary).toHaveBeenCalledWith([
      'HEAD~1',
      '--find-renames',
    ]);
  });

  it('should handle merge conflicts gracefully', async () => {
    mockGit.status.mockResolvedValue({
      conflicted: ['src/conflicted.ts', 'src/another.ts'],
      renamed: [],
      not_added: [],
    });

    mockGit.diffSummary.mockResolvedValue({
      files: [
        {
          file: 'src/normal.ts',
          insertions: 5,
          deletions: 0,
          binary: false,
        },
      ],
    });

    const options: SyncOptions = {
      since: 'HEAD~1',
      verbose: true,
    };

    const result = await diffGit(options);

    expect(result.errors).toContain('Merge conflicts detected in 2 files');
    expect(result.addedFiles).toEqual(['src/normal.ts']); // Should still process other files
  });

  it('should handle invalid git reference', async () => {
    mockGit.revparse.mockRejectedValue(new Error('bad revision'));

    const options: SyncOptions = {
      since: 'invalid-ref',
    };

    const result = await diffGit(options);

    expect(result.errors).toContain('Invalid git reference: invalid-ref');
    expect(result.changedFiles).toEqual([]);
  });

  it('should handle non-git repository', async () => {
    mockGit.checkIsRepo.mockResolvedValue(false);

    const options: SyncOptions = {};
    const result = await diffGit(options);

    expect(result.errors).toContain('Not in a git repository');
    expect(result.changedFiles).toEqual([]);
  });

  it('should warn about large files', async () => {
    const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();

    mockGit.diffSummary.mockResolvedValue({
      files: [
        {
          file: 'src/large.ts',
          insertions: 8000,
          deletions: 3000, // Total: 11000 changes
          binary: false,
        },
      ],
    });

    mockGit.status.mockResolvedValue({
      conflicted: [],
      renamed: [],
      not_added: [],
    });

    const options: SyncOptions = {
      verbose: true,
    };

    const result = await diffGit(options);

    expect(consoleSpy).toHaveBeenCalledWith(
      'Large diff detected in src/large.ts: 11000 changes'
    );
    expect(result.changedFiles).toEqual(['src/large.ts']);

    consoleSpy.mockRestore();
  });

  it('should detect untracked source files in verbose mode', async () => {
    const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

    mockGit.diffSummary.mockResolvedValue({
      files: [],
    });

    mockGit.status.mockResolvedValue({
      conflicted: [],
      renamed: [],
      not_added: ['src/untracked.ts', 'config.json', 'src/another.js'],
    });

    const options: SyncOptions = {
      verbose: true,
    };

    await diffGit(options);

    expect(consoleSpy).toHaveBeenCalledWith(
      'Untracked source files detected: src/untracked.ts, src/another.js'
    );

    consoleSpy.mockRestore();
  });

  it('should skip binary files with verbose logging', async () => {
    const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

    mockGit.diffSummary.mockResolvedValue({
      files: [
        {
          file: 'image.png',
          insertions: 0,
          deletions: 0,
          binary: true,
        },
        {
          file: 'src/code.ts',
          insertions: 5,
          deletions: 2,
          binary: false,
        },
      ],
    });

    mockGit.status.mockResolvedValue({
      conflicted: [],
      renamed: [],
      not_added: [],
    });

    const options: SyncOptions = {
      verbose: true,
    };

    const result = await diffGit(options);

    expect(consoleSpy).toHaveBeenCalledWith('Skipping binary file: image.png');
    expect(result.changedFiles).toEqual(['src/code.ts']);

    consoleSpy.mockRestore();
  });

  it('should skip non-source files with verbose logging', async () => {
    const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

    mockGit.diffSummary.mockResolvedValue({
      files: [
        {
          file: 'package.json',
          insertions: 2,
          deletions: 1,
          binary: false,
        },
        {
          file: 'src/code.ts',
          insertions: 5,
          deletions: 2,
          binary: false,
        },
      ],
    });

    mockGit.status.mockResolvedValue({
      conflicted: [],
      renamed: [],
      not_added: [],
    });

    const options: SyncOptions = {
      verbose: true,
    };

    const result = await diffGit(options);

    expect(consoleSpy).toHaveBeenCalledWith(
      'Skipping non-source file: package.json'
    );
    expect(result.changedFiles).toEqual(['src/code.ts']);

    consoleSpy.mockRestore();
  });

  it('should handle git repository errors', async () => {
    const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation();

    mockGit.checkIsRepo.mockResolvedValue(true);
    mockGit.revparse.mockResolvedValue('abc123');
    mockGit.status.mockResolvedValue({
      conflicted: [],
      renamed: [],
      not_added: [],
    });
    mockGit.diffSummary.mockRejectedValue(new Error('not a git repository'));

    const options: SyncOptions = {
      verbose: true,
    };

    const result = await diffGit(options);

    expect(result.errors).toContain('Not in a git repository');
    expect(consoleErrorSpy).toHaveBeenCalledWith(
      'Git diff error: not a git repository'
    );

    consoleErrorSpy.mockRestore();
  });

  it('should handle bad revision errors', async () => {
    mockGit.revparse.mockRejectedValue(new Error('bad revision'));

    const options: SyncOptions = {
      since: 'invalid-ref',
    };

    const result = await diffGit(options);

    expect(result.errors).toContain('Invalid git reference: invalid-ref');
  });

  it('should handle merge conflict errors', async () => {
    mockGit.diffSummary.mockRejectedValue(new Error('merge conflict detected'));

    const options: SyncOptions = {};

    const result = await diffGit(options);

    expect(result.errors).toContain('Merge conflicts prevent diff operation');
  });

  it('should handle generic git errors', async () => {
    mockGit.diffSummary.mockRejectedValue(new Error('unknown git error'));

    const options: SyncOptions = {};

    const result = await diffGit(options);

    expect(result.errors).toContain('Git diff failed: unknown git error');
  });
});
