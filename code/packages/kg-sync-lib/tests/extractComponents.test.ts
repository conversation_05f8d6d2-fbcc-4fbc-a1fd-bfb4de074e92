/**
 * @fileoverview Tests for component extraction functionality
 */

import { extractComponents } from '../src/extractComponents.js';

describe('extractComponents', () => {
  const milestoneId = 'M1.2';

  describe('Task Breakdown parsing', () => {
    it('should extract components from task breakdown section', () => {
      const content = `
# Milestone M1.2

## Task Breakdown

### Task 01: AnnotationParser
Description of annotation parser task.

### Task 02: GraphUpdater
Description of graph updater task.

### Task 03: CoverageCalculator
Description of coverage calculator task.
`;

      const result = extractComponents(content, milestoneId);

      expect(result).toHaveLength(3);
      expect(result[0]).toEqual({
        name: 'AnnotationParser',
        description: 'Task component: AnnotationParser',
        milestoneId,
        required: true,
      });
      expect(result[1]).toEqual({
        name: 'GraphUpdater',
        description: 'Task component: GraphUpdater',
        milestoneId,
        required: true,
      });
      expect(result[2]).toEqual({
        name: 'CoverageCalculator',
        description: 'Task component: CoverageCalculator',
        milestoneId,
        required: true,
      });
    });

    it('should handle task breakdown with numbers and underscores', () => {
      const content = `
## Task Breakdown

### Task 01: Parser_V2
### Task 02: Graph_Builder_3
`;

      const result = extractComponents(content, milestoneId);

      expect(result).toHaveLength(2);
      expect(result[0]?.name).toBe('Parser_V2');
      expect(result[1]?.name).toBe('Graph_Builder_3');
    });

    it('should return empty array when no task breakdown section', () => {
      const content = `
# Milestone M1.2

## Some Other Section
Content here.
`;

      const result = extractComponents(content, milestoneId);
      expect(result).toHaveLength(0);
    });
  });

  describe('Deliverables parsing', () => {
    it('should extract components from deliverables table', () => {
      const content = `
# Milestone M1.2

## Deliverables

| Component | Description |
|-----------|-------------|
| Parser | Annotation parser |
| Updater | Graph updater |
| Calculator | Coverage calculator |
`;

      const result = extractComponents(content, milestoneId);

      expect(result).toHaveLength(3);
      expect(result[0]).toEqual({
        name: 'Parser',
        description: 'Deliverable component: Parser',
        milestoneId,
        required: true,
      });
      expect(result[1]).toEqual({
        name: 'Updater',
        description: 'Deliverable component: Updater',
        milestoneId,
        required: true,
      });
      expect(result[2]).toEqual({
        name: 'Calculator',
        description: 'Deliverable component: Calculator',
        milestoneId,
        required: true,
      });
    });

    it('should avoid duplicates between task breakdown and deliverables', () => {
      const content = `
## Task Breakdown

### Task 01: Parser
Description.

## Deliverables

| Component | Description |
|-----------|-------------|
| Parser | Same component |
| Updater | Different component |
`;

      const result = extractComponents(content, milestoneId);

      expect(result).toHaveLength(2);
      expect(result.map((c) => c.name)).toEqual(['Parser', 'Updater']);
      expect(result[0]?.description).toBe('Task component: Parser'); // First occurrence wins
    });
  });

  describe('Directory Layout parsing', () => {
    it('should extract components from directory layout', () => {
      const content = `
## Directory Layout

\`\`\`
src/
├── diffGit.ts
├── parseAnnotations.ts
├── updateGraph.ts
└── index.ts
\`\`\`
`;

      const result = extractComponents(content, milestoneId);

      expect(result).toHaveLength(4);
      expect(result[0]).toEqual({
        name: 'DiffGit',
        description: 'File-based component: diffGit.ts',
        milestoneId,
        sourceFile: 'diffGit.ts',
        required: true,
      });
      expect(result[1]).toEqual({
        name: 'ParseAnnotations',
        description: 'File-based component: parseAnnotations.ts',
        milestoneId,
        sourceFile: 'parseAnnotations.ts',
        required: true,
      });
    });

    it('should convert file names to PascalCase', () => {
      const content = `
## Directory Layout

\`\`\`
├── parse-annotations.ts
├── update_graph.ts
├── simple-file.ts
\`\`\`
`;

      const result = extractComponents(content, milestoneId);

      expect(result).toHaveLength(3);
      expect(result[0]?.name).toBe('ParseAnnotations');
      expect(result[1]?.name).toBe('UpdateGraph');
      expect(result[2]?.name).toBe('SimpleFile');
    });

    it('should avoid duplicates with other strategies', () => {
      const content = `
## Task Breakdown

### Task 01: DiffGit
Description.

## Directory Layout

\`\`\`
├── diffGit.ts
├── newFile.ts
\`\`\`
`;

      const result = extractComponents(content, milestoneId);

      expect(result).toHaveLength(2);
      expect(result.map((c) => c.name)).toEqual(['DiffGit', 'NewFile']);
    });
  });

  describe('Multiple strategies combined', () => {
    it('should combine all strategies and avoid duplicates', () => {
      const content = `
## Task Breakdown

### Task 01: AnnotationParser
### Task 02: GraphUpdater

## Deliverables

| Component | Description |
|-----------|-------------|
| AnnotationParser | Same as task |
| CoverageCalculator | New component |

## Directory Layout

\`\`\`
├── diffGit.ts
├── parseAnnotations.ts
\`\`\`
`;

      const result = extractComponents(content, milestoneId);

      expect(result).toHaveLength(5);
      const names = result.map((c) => c.name);
      expect(names).toContain('AnnotationParser');
      expect(names).toContain('GraphUpdater');
      expect(names).toContain('CoverageCalculator');
      expect(names).toContain('DiffGit');
      expect(names).toContain('ParseAnnotations');

      // Should not have duplicates
      expect(new Set(names).size).toBe(names.length);
    });
  });

  describe('Edge cases', () => {
    it('should handle empty content', () => {
      const result = extractComponents('', milestoneId);
      expect(result).toHaveLength(0);
    });

    it('should handle malformed sections', () => {
      const content = `
## Task Breakdown
### Invalid task format
### Task: Missing number

## Deliverables
| Invalid | table format
| Missing | pipe |

## Directory Layout
Invalid file format
`;

      const result = extractComponents(content, milestoneId);
      expect(result).toHaveLength(0);
    });

    it('should handle sections with no matching patterns', () => {
      const content = `
## Task Breakdown
Some text but no task patterns.

## Deliverables
| NoMatch | Description |

## Directory Layout
No .ts files here.
`;

      const result = extractComponents(content, milestoneId);
      expect(result).toHaveLength(0);
    });
  });
});
