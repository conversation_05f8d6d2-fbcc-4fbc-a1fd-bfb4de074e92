/**
 * @fileoverview Tests for graph update functionality
 */

import { updateGraph } from '../src/updateGraph.js';
import type {
  Annotation,
  KnowledgeGraphNode,
  KnowledgeGraphEdge,
} from '../src/types.js';

describe('updateGraph', () => {
  const mockAnnotation: Annotation = {
    milestoneId: 'M1.2',
    componentName: 'TestComponent',
    functionName: 'testFunction',
    filePath: 'src/test.ts',
    lineNumber: 10,
    confidence: 1.0,
    lastVerified: '2025-06-01T12:00:00.000Z',
    errors: [],
  };

  const mockFunctionNode: KnowledgeGraphNode = {
    '@id': 'function:src/test.ts#testFunction',
    '@type': 'function',
    filePath: 'src/test.ts',
    functionName: 'testFunction',
    lastVerified: '2025-06-01T11:00:00.000Z',
  };

  const mockComponentNode: KnowledgeGraphNode = {
    '@id': 'component:M1.2#TestComponent',
    '@type': 'component',
    milestoneId: 'M1.2',
    componentName: 'TestComponent',
    lastVerified: '2025-06-01T11:00:00.000Z',
  };

  const mockImplementsEdge: KnowledgeGraphEdge = {
    '@id':
      'implements:function:src/test.ts#testFunction->component:M1.2#TestComponent',
    '@type': 'implements',
    source: 'function:src/test.ts#testFunction',
    target: 'component:M1.2#TestComponent',
    confidence: 0.8,
    lastVerified: '2025-06-01T11:00:00.000Z',
  };

  describe('new annotations', () => {
    it('should add new function and component nodes for new annotations', () => {
      const currentGraph = { nodes: [], edges: [] };
      const newAnnotations = [mockAnnotation];
      const changedFiles = ['src/test.ts'];

      const result = updateGraph(currentGraph, newAnnotations, changedFiles);

      expect(result.nodesAdded).toBe(2); // function + component
      expect(result.nodesUpdated).toBe(0);
      expect(result.edgesAdded).toBe(1); // implements edge
      expect(result.edgesUpdated).toBe(0);
      expect(result.errors).toHaveLength(0);
    });

    it('should update existing function node when annotation exists', () => {
      const currentGraph = {
        nodes: [mockFunctionNode],
        edges: [],
      };
      const newAnnotations = [mockAnnotation];
      const changedFiles = ['src/test.ts'];

      const result = updateGraph(currentGraph, newAnnotations, changedFiles);

      expect(result.nodesAdded).toBe(1); // only component node
      expect(result.nodesUpdated).toBe(1); // function node updated
      expect(result.edgesAdded).toBe(1);
      expect(result.errors).toHaveLength(0);
    });

    it('should update existing edge when annotation exists', () => {
      const currentGraph = {
        nodes: [mockFunctionNode, mockComponentNode],
        edges: [mockImplementsEdge],
      };
      const newAnnotations = [mockAnnotation];
      const changedFiles = ['src/test.ts'];

      const result = updateGraph(currentGraph, newAnnotations, changedFiles);

      expect(result.nodesAdded).toBe(0);
      expect(result.nodesUpdated).toBe(1); // function node updated
      expect(result.edgesAdded).toBe(0);
      expect(result.edgesUpdated).toBe(1); // edge updated
      expect(result.errors).toHaveLength(0);
    });

    it('should reset stale flag when annotation is restored', () => {
      const staleEdge: KnowledgeGraphEdge = {
        ...mockImplementsEdge,
        stale: true,
        staleReason: 'annotation_removed',
        confidence: 0.2,
      };

      const currentGraph = {
        nodes: [mockFunctionNode, mockComponentNode],
        edges: [staleEdge],
      };
      const newAnnotations = [mockAnnotation];
      const changedFiles = ['src/test.ts'];

      const result = updateGraph(currentGraph, newAnnotations, changedFiles);

      expect(result.edgesUpdated).toBe(1);
      expect(result.errors).toHaveLength(0);
    });
  });

  describe('multiple annotations', () => {
    it('should handle multiple annotations correctly', () => {
      const annotation2: Annotation = {
        ...mockAnnotation,
        componentName: 'AnotherComponent',
        functionName: 'anotherFunction',
      };

      const currentGraph = { nodes: [], edges: [] };
      const newAnnotations = [mockAnnotation, annotation2];
      const changedFiles = ['src/test.ts'];

      const result = updateGraph(currentGraph, newAnnotations, changedFiles);

      expect(result.nodesAdded).toBe(4); // 2 functions + 2 components
      expect(result.edgesAdded).toBe(2); // 2 implements edges
      expect(result.errors).toHaveLength(0);
    });

    it('should avoid duplicate component nodes', () => {
      const annotation2: Annotation = {
        ...mockAnnotation,
        functionName: 'anotherFunction',
        // Same component name
      };

      const currentGraph = { nodes: [], edges: [] };
      const newAnnotations = [mockAnnotation, annotation2];
      const changedFiles = ['src/test.ts'];

      const result = updateGraph(currentGraph, newAnnotations, changedFiles);

      expect(result.nodesAdded).toBe(3); // 2 functions + 1 component (shared)
      expect(result.edgesAdded).toBe(2); // 2 implements edges
      expect(result.errors).toHaveLength(0);
    });
  });

  describe('stale detection', () => {
    it('should mark edges as stale when no corresponding annotation', () => {
      const currentGraph = {
        nodes: [mockFunctionNode, mockComponentNode],
        edges: [mockImplementsEdge],
      };
      const newAnnotations: Annotation[] = []; // No annotations
      const changedFiles = ['src/test.ts'];

      const result = updateGraph(currentGraph, newAnnotations, changedFiles);

      expect(result.edgesMarkedStale).toBe(1);
      expect(result.errors).toHaveLength(0);
    });

    it('should not mark already stale edges as stale again', () => {
      const staleEdge: KnowledgeGraphEdge = {
        ...mockImplementsEdge,
        stale: true,
        staleReason: 'annotation_removed',
      };

      const currentGraph = {
        nodes: [mockFunctionNode, mockComponentNode],
        edges: [staleEdge],
      };
      const newAnnotations: Annotation[] = [];
      const changedFiles = ['src/test.ts'];

      const result = updateGraph(currentGraph, newAnnotations, changedFiles);

      expect(result.edgesMarkedStale).toBe(0); // Already stale
      expect(result.errors).toHaveLength(0);
    });
  });

  describe('error handling', () => {
    it('should handle errors gracefully and continue processing', () => {
      const invalidAnnotation: Annotation = {
        ...mockAnnotation,
        functionName: '', // Invalid function name
      };

      const currentGraph = { nodes: [], edges: [] };
      const newAnnotations = [invalidAnnotation, mockAnnotation];
      const changedFiles = ['src/test.ts'];

      const result = updateGraph(currentGraph, newAnnotations, changedFiles);

      // Should still process the valid annotation
      expect(result.nodesAdded).toBeGreaterThan(0);
      expect(result.edgesAdded).toBeGreaterThan(0);
    });
  });

  describe('edge cases', () => {
    it('should handle empty current graph', () => {
      const currentGraph = { nodes: [], edges: [] };
      const newAnnotations = [mockAnnotation];
      const changedFiles = ['src/test.ts'];

      const result = updateGraph(currentGraph, newAnnotations, changedFiles);

      expect(result.nodesAdded).toBe(2);
      expect(result.edgesAdded).toBe(1);
      expect(result.errors).toHaveLength(0);
    });

    it('should handle empty annotations', () => {
      const currentGraph = {
        nodes: [mockFunctionNode, mockComponentNode],
        edges: [mockImplementsEdge],
      };
      const newAnnotations: Annotation[] = [];
      const changedFiles: string[] = [];

      const result = updateGraph(currentGraph, newAnnotations, changedFiles);

      expect(result.nodesAdded).toBe(0);
      expect(result.nodesUpdated).toBe(0);
      expect(result.edgesAdded).toBe(0);
      expect(result.edgesUpdated).toBe(0);
      expect(result.edgesMarkedStale).toBe(1); // Existing edge marked stale
      expect(result.errors).toHaveLength(0);
    });

    it('should handle annotations with different confidence scores', () => {
      const lowConfidenceAnnotation: Annotation = {
        ...mockAnnotation,
        confidence: 0.5,
      };

      const currentGraph = { nodes: [], edges: [] };
      const newAnnotations = [lowConfidenceAnnotation];
      const changedFiles = ['src/test.ts'];

      const result = updateGraph(currentGraph, newAnnotations, changedFiles);

      expect(result.nodesAdded).toBe(2);
      expect(result.edgesAdded).toBe(1);
      expect(result.errors).toHaveLength(0);
    });
  });
});
