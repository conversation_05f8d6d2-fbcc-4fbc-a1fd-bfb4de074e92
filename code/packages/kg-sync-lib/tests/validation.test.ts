/**
 * @fileoverview Tests for enhanced validation functionality
 */

import {
  validateAnnotations,
  validateAnnotationFormat,
} from '../src/validation.js';
import type { Annotation } from '../src/types.js';

describe('validateAnnotations', () => {
  it('should detect duplicate annotations', () => {
    const annotations: Annotation[] = [
      {
        milestoneId: 'M1.2',
        componentName: 'DuplicateComponent',
        functionName: 'function1',
        filePath: 'src/test1.ts',
        lineNumber: 5,
        confidence: 1.0,
        lastVerified: '2023-01-01T00:00:00.000Z',
        errors: [],
      },
      {
        milestoneId: 'M1.2',
        componentName: 'DuplicateComponent',
        functionName: 'function2',
        filePath: 'src/test2.ts',
        lineNumber: 10,
        confidence: 1.0,
        lastVerified: '2023-01-01T00:00:00.000Z',
        errors: [],
      },
    ];

    const result = validateAnnotations(annotations);

    expect(result).toHaveLength(2);
    expect(result[0]!.errors.length).toBeGreaterThan(0);
    expect(result[1]!.errors.length).toBeGreaterThan(0);
    expect(
      result[0]!.errors.some((e) =>
        e.message.includes('Multiple @implements annotations')
      )
    ).toBe(true);
    expect(
      result[1]!.errors.some((e) =>
        e.message.includes('Duplicate @implements annotation')
      )
    ).toBe(true);
  });

  it('should validate milestone ID format', () => {
    const annotations: Annotation[] = [
      {
        milestoneId: 'InvalidFormat',
        componentName: 'TestComponent',
        functionName: 'testFunction',
        filePath: 'src/test.ts',
        lineNumber: 5,
        confidence: 1.0,
        lastVerified: '2023-01-01T00:00:00.000Z',
        errors: [],
      },
    ];

    const result = validateAnnotations(annotations);

    expect(result[0]!.errors.length).toBeGreaterThan(0);
    expect(
      result[0]!.errors.some((e) =>
        e.message.includes('Invalid milestone ID format')
      )
    ).toBe(true);
    expect(result[0]!.errors.some((e) => e.severity === 'error')).toBe(true);
  });

  it('should warn about large milestone numbers', () => {
    const annotations: Annotation[] = [
      {
        milestoneId: 'M1000.2000',
        componentName: 'TestComponent',
        functionName: 'testFunction',
        filePath: 'src/test.ts',
        lineNumber: 5,
        confidence: 1.0,
        lastVerified: '2023-01-01T00:00:00.000Z',
        errors: [],
      },
    ];

    const result = validateAnnotations(annotations);

    expect(result[0]!.errors.length).toBeGreaterThanOrEqual(2); // At least two warnings for large numbers
    expect(
      result[0]!.errors.some((e) =>
        e.message.includes('Milestone number too large')
      )
    ).toBe(true);
    expect(result[0]!.errors.some((e) => e.severity === 'warning')).toBe(true);
  });

  it('should validate component name format', () => {
    const annotations: Annotation[] = [
      {
        milestoneId: 'M1.2',
        componentName: '123InvalidName',
        functionName: 'testFunction',
        filePath: 'src/test.ts',
        lineNumber: 5,
        confidence: 1.0,
        lastVerified: '2023-01-01T00:00:00.000Z',
        errors: [],
      },
    ];

    const result = validateAnnotations(annotations);

    expect(result[0]!.errors.length).toBeGreaterThan(0);
    expect(
      result[0]!.errors.some((e) =>
        e.message.includes('Invalid component name format')
      )
    ).toBe(true);
    expect(result[0]!.errors.some((e) => e.severity === 'error')).toBe(true);
  });

  it('should warn about reserved component names', () => {
    const annotations: Annotation[] = [
      {
        milestoneId: 'M1.2',
        componentName: 'constructor',
        functionName: 'testFunction',
        filePath: 'src/test.ts',
        lineNumber: 5,
        confidence: 1.0,
        lastVerified: '2023-01-01T00:00:00.000Z',
        errors: [],
      },
    ];

    const result = validateAnnotations(annotations);

    expect(result[0]!.errors.length).toBeGreaterThan(0);
    expect(
      result[0]!.errors.some((e) =>
        e.message.includes('Reserved component name')
      )
    ).toBe(true);
    expect(result[0]!.errors.some((e) => e.severity === 'warning')).toBe(true);
  });

  it('should warn about short component names', () => {
    const annotations: Annotation[] = [
      {
        milestoneId: 'M1.2',
        componentName: 'AB',
        functionName: 'testFunction',
        filePath: 'src/test.ts',
        lineNumber: 5,
        confidence: 1.0,
        lastVerified: '2023-01-01T00:00:00.000Z',
        errors: [],
      },
    ];

    const result = validateAnnotations(annotations);

    expect(result[0]!.errors.length).toBeGreaterThan(0);
    expect(
      result[0]!.errors.some((e) =>
        e.message.includes('Component name too short')
      )
    ).toBe(true);
    expect(result[0]!.errors.some((e) => e.severity === 'warning')).toBe(true);
  });

  it('should warn about long component names', () => {
    const annotations: Annotation[] = [
      {
        milestoneId: 'M1.2',
        componentName: 'VeryLongComponentNameThatExceedsFiftyCharactersLimit',
        functionName: 'testFunction',
        filePath: 'src/test.ts',
        lineNumber: 5,
        confidence: 1.0,
        lastVerified: '2023-01-01T00:00:00.000Z',
        errors: [],
      },
    ];

    const result = validateAnnotations(annotations);

    expect(result[0]!.errors.length).toBeGreaterThan(0);
    expect(
      result[0]!.errors.some((e) =>
        e.message.includes('Component name too long')
      )
    ).toBe(true);
    expect(result[0]!.errors.some((e) => e.severity === 'warning')).toBe(true);
  });

  it('should warn about non-PascalCase component names', () => {
    const annotations: Annotation[] = [
      {
        milestoneId: 'M1.2',
        componentName: 'camelCaseComponent',
        functionName: 'testFunction',
        filePath: 'src/test.ts',
        lineNumber: 5,
        confidence: 1.0,
        lastVerified: '2023-01-01T00:00:00.000Z',
        errors: [],
      },
    ];

    const result = validateAnnotations(annotations);

    expect(result[0]!.errors.length).toBeGreaterThan(0);
    expect(
      result[0]!.errors.some((e) => e.message.includes('should use PascalCase'))
    ).toBe(true);
    expect(result[0]!.errors.some((e) => e.severity === 'warning')).toBe(true);
  });

  it('should recalculate confidence based on errors', () => {
    const annotations: Annotation[] = [
      {
        milestoneId: 'InvalidFormat',
        componentName: '123Invalid',
        functionName: 'testFunction',
        filePath: 'src/test.ts',
        lineNumber: 5,
        confidence: 1.0,
        lastVerified: '2023-01-01T00:00:00.000Z',
        errors: [],
      },
    ];

    const result = validateAnnotations(annotations);

    expect(result[0]!.confidence).toBeLessThan(1.0);
    expect(result[0]!.confidence).toBeGreaterThan(0.0);
  });

  it('should handle annotations without errors', () => {
    const annotations: Annotation[] = [
      {
        milestoneId: 'M1.2',
        componentName: 'ValidComponent',
        functionName: 'validFunction',
        filePath: 'src/validComponent.ts',
        lineNumber: 5,
        confidence: 1.0,
        lastVerified: '2023-01-01T00:00:00.000Z',
        errors: [],
      },
    ];

    const result = validateAnnotations(annotations);

    expect(result[0]!.errors).toHaveLength(0);
    expect(result[0]!.confidence).toBe(1.0);
  });
});

describe('validateAnnotationFormat', () => {
  it('should detect empty annotations', () => {
    const errors = validateAnnotationFormat('', 5);

    expect(errors).toHaveLength(1);
    expect(errors[0]!.message).toContain('empty');
    expect(errors[0]!.severity).toBe('error');
  });

  it('should detect missing milestone prefix', () => {
    const errors = validateAnnotationFormat('M1.2#Component', 5);

    expect(errors).toHaveLength(1);
    expect(errors[0]!.message).toContain('Missing "milestone-" prefix');
    expect(errors[0]!.severity).toBe('error');
  });

  it('should detect missing hash separator', () => {
    const errors = validateAnnotationFormat('milestone-M1.2Component', 5);

    expect(errors).toHaveLength(1);
    expect(errors[0]!.message).toContain('Missing "#" separator');
    expect(errors[0]!.severity).toBe('error');
  });

  it('should detect multiple hash characters', () => {
    const errors = validateAnnotationFormat(
      'milestone-M1.2#Component#Extra',
      5
    );

    expect(errors).toHaveLength(1);
    expect(errors[0]!.message).toContain('Multiple "#" characters');
    expect(errors[0]!.severity).toBe('error');
  });

  it('should return no errors for valid format', () => {
    const errors = validateAnnotationFormat('milestone-M1.2#ValidComponent', 5);

    expect(errors).toHaveLength(0);
  });
});
